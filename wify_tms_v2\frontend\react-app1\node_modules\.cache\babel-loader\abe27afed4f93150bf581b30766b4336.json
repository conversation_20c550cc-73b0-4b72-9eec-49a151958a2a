{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\WifyNotifications\\\\NotificationItem.js\";\nimport React from 'react';\nimport { convertUTCToDisplayTime, isTodayDate, isYesterdayDate } from '../../util/helpers';\nimport { TaskRminderTemplate } from './NotificationTemplates/TaskReminder';\nimport { TaskAssigneeAndReassignTemplate } from './NotificationTemplates/TaskAssigneeAndReassignTemplate';\nimport { DueTaskTemplate } from './NotificationTemplates/DueTaskTemplate';\nimport { TaskDeletionTemplate } from './NotificationTemplates/TaskDeletionTemplate';\nimport { SrvcReqStatusUpdateViaVA } from './NotificationTemplates/SrvcReqStatusUpdateViaVA';\nconst NotificationItem = ({\n  notification,\n  markAsRead,\n  removeNotifcationFromApp\n}) => {\n  const {\n    notification_type,\n    notification_time\n  } = notification;\n  const isCurrentDate = isTodayDate(notification_time);\n  const isYesterday = isYesterdayDate(notification_time);\n  let showTimeonly = false,\n    showDateOnly = false;\n  if (isCurrentDate) {\n    showTimeonly = true;\n  } else {\n    showDateOnly = true;\n  }\n  let notiTimeLabel = isYesterday ? 'Yesterday' : `${convertUTCToDisplayTime(notification_time, showDateOnly, showTimeonly)}`;\n  let element;\n  switch (notification_type) {\n    case 'my_tasks_noti':\n      element = DueTaskTemplate(notification, markAsRead, removeNotifcationFromApp, notiTimeLabel, isCurrentDate);\n      break;\n    case 'task_assignee':\n      element = TaskAssigneeAndReassignTemplate(notification, markAsRead, removeNotifcationFromApp, notiTimeLabel);\n      break;\n    case 'task_deletion':\n      element = TaskDeletionTemplate(notification, markAsRead, removeNotifcationFromApp, notiTimeLabel);\n      break;\n    case 'task_reassignee':\n      element = TaskAssigneeAndReassignTemplate(notification, markAsRead, removeNotifcationFromApp, notiTimeLabel);\n      break;\n    case 'task_reminder':\n      element = TaskRminderTemplate(notification, markAsRead, removeNotifcationFromApp, notiTimeLabel);\n    case 'srvc_req_status_update_thr_va':\n      element = SrvcReqStatusUpdateViaVA(notification, markAsRead, removeNotifcationFromApp, notiTimeLabel);\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wifyNotification gx-my-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }\n  }, element));\n};\nexport default NotificationItem;", "map": {"version": 3, "names": ["React", "convertUTCToDisplayTime", "isTodayDate", "isYesterdayDate", "TaskRminderTemplate", "TaskAssigneeAndReassignTemplate", "DueTaskTemplate", "TaskDeletionTemplate", "SrvcReqStatusUpdateViaVA", "NotificationItem", "notification", "mark<PERSON><PERSON><PERSON>", "removeNotifcationFromApp", "notification_type", "notification_time", "isCurrentDate", "isYesterday", "showTimeonly", "showDateOnly", "notiTimeLabel", "element", "createElement", "Fragment", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/WifyNotifications/NotificationItem.js"], "sourcesContent": ["import React from 'react';\r\nimport {\r\n    convertUTCToDisplayTime,\r\n    isTodayDate,\r\n    isYesterdayDate,\r\n} from '../../util/helpers';\r\nimport { TaskRminderTemplate } from './NotificationTemplates/TaskReminder';\r\nimport { TaskAssigneeAndReassignTemplate } from './NotificationTemplates/TaskAssigneeAndReassignTemplate';\r\nimport { DueTaskTemplate } from './NotificationTemplates/DueTaskTemplate';\r\nimport { TaskDeletionTemplate } from './NotificationTemplates/TaskDeletionTemplate';\r\nimport { SrvcReqStatusUpdateViaVA } from './NotificationTemplates/SrvcReqStatusUpdateViaVA';\r\n\r\nconst NotificationItem = ({\r\n    notification,\r\n    markAsRead,\r\n    removeNotifcationFromApp,\r\n}) => {\r\n    const { notification_type, notification_time } = notification;\r\n    const isCurrentDate = isTodayDate(notification_time);\r\n    const isYesterday = isYesterdayDate(notification_time);\r\n\r\n    let showTimeonly = false,\r\n        showDateOnly = false;\r\n    if (isCurrentDate) {\r\n        showTimeonly = true;\r\n    } else {\r\n        showDateOnly = true;\r\n    }\r\n\r\n    let notiTimeLabel = isYesterday\r\n        ? 'Yesterday'\r\n        : `${convertUTCToDisplayTime(\r\n              notification_time,\r\n              showDateOnly,\r\n              showTimeonly\r\n          )}`;\r\n    let element;\r\n\r\n    switch (notification_type) {\r\n        case 'my_tasks_noti':\r\n            element = DueTaskTemplate(\r\n                notification,\r\n                markAsRead,\r\n                removeNotifcationFromApp,\r\n                notiTimeLabel,\r\n                isCurrentDate\r\n            );\r\n            break;\r\n        case 'task_assignee':\r\n            element = TaskAssigneeAndReassignTemplate(\r\n                notification,\r\n                markAsRead,\r\n                removeNotifcationFromApp,\r\n                notiTimeLabel\r\n            );\r\n            break;\r\n        case 'task_deletion':\r\n            element = TaskDeletionTemplate(\r\n                notification,\r\n                markAsRead,\r\n                removeNotifcationFromApp,\r\n                notiTimeLabel\r\n            );\r\n            break;\r\n        case 'task_reassignee':\r\n            element = TaskAssigneeAndReassignTemplate(\r\n                notification,\r\n                markAsRead,\r\n                removeNotifcationFromApp,\r\n                notiTimeLabel\r\n            );\r\n            break;\r\n        case 'task_reminder':\r\n            element = TaskRminderTemplate(\r\n                notification,\r\n                markAsRead,\r\n                removeNotifcationFromApp,\r\n                notiTimeLabel\r\n            );\r\n        case 'srvc_req_status_update_thr_va':\r\n            element = SrvcReqStatusUpdateViaVA(\r\n                notification,\r\n                markAsRead,\r\n                removeNotifcationFromApp,\r\n                notiTimeLabel\r\n            );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <div className=\"wifyNotification gx-my-1\">{element}</div>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default NotificationItem;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,uBAAuB,EACvBC,WAAW,EACXC,eAAe,QACZ,oBAAoB;AAC3B,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,+BAA+B,QAAQ,yDAAyD;AACzG,SAASC,eAAe,QAAQ,yCAAyC;AACzE,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,wBAAwB,QAAQ,kDAAkD;AAE3F,MAAMC,gBAAgB,GAAGA,CAAC;EACtBC,YAAY;EACZC,UAAU;EACVC;AACJ,CAAC,KAAK;EACF,MAAM;IAAEC,iBAAiB;IAAEC;EAAkB,CAAC,GAAGJ,YAAY;EAC7D,MAAMK,aAAa,GAAGb,WAAW,CAACY,iBAAiB,CAAC;EACpD,MAAME,WAAW,GAAGb,eAAe,CAACW,iBAAiB,CAAC;EAEtD,IAAIG,YAAY,GAAG,KAAK;IACpBC,YAAY,GAAG,KAAK;EACxB,IAAIH,aAAa,EAAE;IACfE,YAAY,GAAG,IAAI;EACvB,CAAC,MAAM;IACHC,YAAY,GAAG,IAAI;EACvB;EAEA,IAAIC,aAAa,GAAGH,WAAW,GACzB,WAAW,GACX,GAAGf,uBAAuB,CACtBa,iBAAiB,EACjBI,YAAY,EACZD,YACJ,CAAC,EAAE;EACT,IAAIG,OAAO;EAEX,QAAQP,iBAAiB;IACrB,KAAK,eAAe;MAChBO,OAAO,GAAGd,eAAe,CACrBI,YAAY,EACZC,UAAU,EACVC,wBAAwB,EACxBO,aAAa,EACbJ,aACJ,CAAC;MACD;IACJ,KAAK,eAAe;MAChBK,OAAO,GAAGf,+BAA+B,CACrCK,YAAY,EACZC,UAAU,EACVC,wBAAwB,EACxBO,aACJ,CAAC;MACD;IACJ,KAAK,eAAe;MAChBC,OAAO,GAAGb,oBAAoB,CAC1BG,YAAY,EACZC,UAAU,EACVC,wBAAwB,EACxBO,aACJ,CAAC;MACD;IACJ,KAAK,iBAAiB;MAClBC,OAAO,GAAGf,+BAA+B,CACrCK,YAAY,EACZC,UAAU,EACVC,wBAAwB,EACxBO,aACJ,CAAC;MACD;IACJ,KAAK,eAAe;MAChBC,OAAO,GAAGhB,mBAAmB,CACzBM,YAAY,EACZC,UAAU,EACVC,wBAAwB,EACxBO,aACJ,CAAC;IACL,KAAK,+BAA+B;MAChCC,OAAO,GAAGZ,wBAAwB,CAC9BE,YAAY,EACZC,UAAU,EACVC,wBAAwB,EACxBO,aACJ,CAAC;EACT;EAEA,oBACInB,KAAA,CAAAqB,aAAA,CAAArB,KAAA,CAAAsB,QAAA,qBACItB,KAAA,CAAAqB,aAAA;IAAKE,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAET,OAAa,CAC1D,CAAC;AAEX,CAAC;AAED,eAAeX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}