{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\routes\\\\setup\\\\srvc-req\\\\settings\\\\CustomerSettings.js\";\nimport React from 'react';\nimport { Alert, Form, Tooltip, Checkbox } from 'antd';\nimport { QuestionCircleOutlined } from '@ant-design/icons';\nimport FormBuilder from 'antd-form-builder';\nconst CustomerSettings = ({\n  formRef\n}) => {\n  const getCustomerSettingsMeta = () => {\n    return {\n      columns: 1,\n      formItemLayout: null,\n      fields: [{\n        key: 'enable_customer_profile_creation_based_on_name',\n        render: ({\n          form,\n          field\n        }) => {\n          return /*#__PURE__*/React.createElement(Form.Item, {\n            name: field.key,\n            valuePropName: \"checked\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 29\n            }\n          }, /*#__PURE__*/React.createElement(Checkbox, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 33\n            }\n          }, \"Enable customer profile creation based on name\", /*#__PURE__*/React.createElement(Tooltip, {\n            title: \"Customer will be created based on unique customer name and address combination (instead of mobile number)\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 37\n            }\n          }, /*#__PURE__*/React.createElement(QuestionCircleOutlined, {\n            className: \"gx-ml-2 gx-text-blue\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 41\n            }\n          }))));\n        }\n      }]\n    };\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Alert, {\n    description: \"This setting is used to define customer level configurations for customers of the organization.\",\n    type: \"info\",\n    showIcon: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(FormBuilder, {\n    meta: getCustomerSettingsMeta(),\n    form: formRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }\n  }));\n};\nexport default CustomerSettings;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "Form", "<PERSON><PERSON><PERSON>", "Checkbox", "QuestionCircleOutlined", "FormBuilder", "CustomerSettings", "formRef", "getCustomerSettingsMeta", "columns", "formItemLayout", "fields", "key", "render", "form", "field", "createElement", "<PERSON><PERSON>", "name", "valuePropName", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "className", "Fragment", "description", "type", "showIcon", "meta"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/routes/setup/srvc-req/settings/CustomerSettings.js"], "sourcesContent": ["import React from 'react';\r\nimport { Alert, Form, Tooltip, Checkbox } from 'antd';\r\nimport { QuestionCircleOutlined } from '@ant-design/icons';\r\nimport FormBuilder from 'antd-form-builder';\r\n\r\nconst CustomerSettings = ({ formRef }) => {\r\n    const getCustomerSettingsMeta = () => {\r\n        return {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: 'enable_customer_profile_creation_based_on_name',\r\n                    render: ({ form, field }) => {\r\n                        return (\r\n                            <Form.Item name={field.key} valuePropName=\"checked\">\r\n                                <Checkbox>\r\n                                    Enable customer profile creation based on name\r\n                                    <Tooltip title=\"Customer will be created based on unique customer name and address combination (instead of mobile number)\">\r\n                                        <QuestionCircleOutlined  className='gx-ml-2 gx-text-blue' />\r\n                                    </Tooltip>\r\n                                </Checkbox>\r\n                            </Form.Item>\r\n                        );\r\n                    }\r\n                },\r\n            ],\r\n        };\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Alert\r\n                description=\"This setting is used to define customer level configurations for customers of the organization.\"\r\n                type=\"info\"\r\n                showIcon\r\n            />\r\n            <FormBuilder\r\n                meta={getCustomerSettingsMeta()}\r\n                form={formRef}\r\n            />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default CustomerSettings;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,MAAM;AACrD,SAASC,sBAAsB,QAAQ,mBAAmB;AAC1D,OAAOC,WAAW,MAAM,mBAAmB;AAE3C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACtC,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IAClC,OAAO;MACHC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,gDAAgD;QACrDC,MAAM,EAAEA,CAAC;UAAEC,IAAI;UAAEC;QAAM,CAAC,KAAK;UACzB,oBACIhB,KAAA,CAAAiB,aAAA,CAACf,IAAI,CAACgB,IAAI;YAACC,IAAI,EAAEH,KAAK,CAACH,GAAI;YAACO,aAAa,EAAC,SAAS;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAC/C1B,KAAA,CAAAiB,aAAA,CAACb,QAAQ;YAAAiB,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAC,gDAEN,eAAA1B,KAAA,CAAAiB,aAAA,CAACd,OAAO;YAACwB,KAAK,EAAC,2GAA2G;YAAAN,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBACtH1B,KAAA,CAAAiB,aAAA,CAACZ,sBAAsB;YAAEuB,SAAS,EAAC,sBAAsB;YAAAP,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CACtD,CACH,CACH,CAAC;QAEpB;MACJ,CAAC;IAET,CAAC;EACL,CAAC;EAED,oBACI1B,KAAA,CAAAiB,aAAA,CAAAjB,KAAA,CAAA6B,QAAA,qBACI7B,KAAA,CAAAiB,aAAA,CAAChB,KAAK;IACF6B,WAAW,EAAC,iGAAiG;IAC7GC,IAAI,EAAC,MAAM;IACXC,QAAQ;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eACF1B,KAAA,CAAAiB,aAAA,CAACX,WAAW;IACR2B,IAAI,EAAExB,uBAAuB,CAAC,CAAE;IAChCM,IAAI,EAAEP,OAAQ;IAAAa,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACjB,CACH,CAAC;AAEX,CAAC;AAED,eAAenB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}