import React, { useState } from 'react';
import { PiEnvelopeOpenLight, PiEnvelopeSimpleLight } from 'react-icons/pi';
import UserName from '../../wify-utils/UserName';
import {
    formatDate,
    removeAndAppendSixDigitIntegers,
} from '../../../routes/users/helper';
import { Modal } from 'antd';

export const SrvcReqStatusUpdateViaVA = (
    notification,
    markAsRead,
    removeNotifcationFromApp,
    notiTimeLabel
) => {
    const { notification_message, notification_title, form_data } =
        notification;
    const dbResp = form_data?.dbResp || {};
    const status = dbResp?.status;
    const failed_entries = dbResp?.failed_entries || [];
    const result = form_data?.result || {};
    const [openModel, setOpenModel] = useState(false);

    //console.log(formattedAddressFrReminder.data , truncatedAddressFrReminder)
    const onClickOpenModel = () => {
        setOpenModel(!openModel);
    };

    return (
        <>
            <div className="wy-va-service-req-bulk-update-errors">
                <Modal
                    wrapClassName="wy-va-service-req-bulk-update-errors-modal-wrapper-class"
                    title={`Service Req Bulk Update Errors`}
                    visible={openModel}
                    onCancel={() => onClickOpenModel()}
                    width={1500}
                    style={{ marginTop: '-70px' }}
                    bodyStyle={{
                        minHeight: '85vh',
                        padding: '18px',
                        paddingTop: '0px',
                    }}
                    footer={null}
                >
                    <div className="wy-va-service-req-bulk-update-errors-container">
                        {Object.entries(result).map(([errorType, entries]) => (
                            <div key={errorType}>
                                <h3>Errors: {errorType}</h3>
                                <ul>
                                    {entries.map((item, index) => (
                                        <li key={index}>
                                            <strong>{item.display_code}</strong>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        ))}
                    </div>
                </Modal>
            </div>

            <li className="gx-p-0">
                <div
                    className={`notification_wrapper gx-pointer ${
                        notification.is_read == true && ' notification_read'
                    }`}
                    onClick={() => {
                        if (status != 'ALL_UPDATED') {
                            onClickOpenModel();
                        }
                        markAsRead(notification.entry_id, {
                            updation_fr_read: true,
                        });
                        removeNotifcationFromApp(notification.notification_id);
                    }}
                >
                    <div className="gx-d-flex ">
                        <div
                            className={`${
                                notification.is_read == true &&
                                'read_notification'
                            } notification_date_time_wrapper`}
                        >
                            <div className="gx-fs-18">Today</div>
                        </div>
                        <div className="notification_content_wrapper">
                            <div className="gx-media-body gx-align-self-center">
                                <div className="wy-flex-only-row-between">
                                    <p className="gx-fs-sm gx-mb-0 gx-mt-3 gx-d-flex gx-justify-content-start gx-align-items-start">
                                        <div>
                                            {notification.is_read == true ? (
                                                <PiEnvelopeOpenLight className="gx-text-success gx-mr-2 gx-fs-xl" />
                                            ) : (
                                                <PiEnvelopeSimpleLight className="gx-text-danger gx-mr-2 gx-fs-xl" />
                                            )}
                                        </div>
                                        <div
                                            className={
                                                !notification.is_read == true &&
                                                'gx-font-weight-semi-bold'
                                            }
                                        >
                                            {notification_title}
                                        </div>
                                    </p>
                                    <div className="notification-date gx-font-italic">
                                        <small>{`${notiTimeLabel}`}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
        </>
    );
};
