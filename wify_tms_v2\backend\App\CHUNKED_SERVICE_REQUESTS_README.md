# Chunked Service Request Fetching Implementation

## Overview

This implementation optimizes the service request fetching process by breaking down large queries into smaller, manageable chunks. Instead of fetching all service requests at once (which can cause memory issues and timeouts), the system now processes data in 3-month chunks.

## Problem Solved

**Before (Old Implementation):**

- Single large query to fetch all service requests
- High memory usage
- Risk of database timeouts
- Potential memory overflow for large datasets
- All-or-nothing approach (if query fails, everything fails)

**After (New Implementation):**

- Multiple smaller queries in 3-month chunks
- Controlled memory usage
- No query timeouts
- Fault tolerance (continues even if one chunk fails)
- Adaptive chunk sizing based on data volume
- File-based caching for very large datasets

## Files Modified/Created

### 1. Database Function Enhanced

**File:** `wify_tms_v2/backend/App/dbFunctions/tms_get_srvc_req_ids_by_vertical_id.sql`

**Changes:**

- Added optional `start_date` and `end_date` parameters
- Added date filtering in WHERE clause
- Added `creation_date` to the returned data
- Added ORDER BY for consistent results

### 2. Services Model Updated

**File:** `wify_tms_v2/backend/App/api_models/services_model.js`

**Changes:**

- Replaced single database call with `fetchServiceRequestsInChunks()` method
- Added comprehensive chunking logic
- Integrated with ChunkedServiceRequestsManager utility
- Added error handling and logging

### 3. Utility Class Created

**File:** `wify_tms_v2/backend/App/api_models/utils/chunked_service_requests.js`

**Features:**

- Adaptive chunk sizing based on estimated data volume
- File-based caching for large datasets
- Automatic deduplication
- Temporary file management
- Configurable chunking parameters

### 4. Test Script

**File:** `wify_tms_v2/backend/App/test_chunked_service_requests.js`

**Purpose:**

- Demonstrates the difference between old and new implementations
- Shows chunking strategies for different data volumes
- Provides examples of date range calculations

## How It Works

### 1. Chunking Strategy

The system automatically selects the optimal chunking strategy based on estimated data volume:

```javascript
// Small datasets (< 20,000 records)
{
    monthsPerChunk: 3,
    totalMonths: 12,     // 1 year of data
    useFileCache: false  // Keep in memory
}

// Medium datasets (20,000 - 50,000 records)
{
    monthsPerChunk: 2,
    totalMonths: 12,     // 1 year of data
    useFileCache: true   // Use file caching
}

// Large datasets (> 50,000 records)
{
    monthsPerChunk: 1,
    totalMonths: 12,     // 1 year of data
    useFileCache: true   // Use file caching
}
```

### 2. Date Range Calculation

For each chunk, the system calculates a date range:

```
Current Date: 2025-01-10
Chunk 1: 2024-10-10 to 2025-01-10 (3 months)
Chunk 2: 2024-07-10 to 2024-10-10 (3 months)
Chunk 3: 2024-04-10 to 2024-07-10 (3 months)
Chunk 4: 2024-01-10 to 2024-04-10 (3 months)
Total: 12 months (1 year)
```

### 3. Processing Flow

1. **Initialize:** Create ChunkedServiceRequestsManager instance
2. **Configure:** Determine optimal chunk size and caching strategy
3. **Process Chunks:** For each time period:
    - Calculate date range
    - Query database with date filters
    - Store results (memory or file)
    - Log progress
4. **Consolidate:** Combine all chunk results
5. **Deduplicate:** Remove any duplicate records
6. **Cleanup:** Remove temporary files if used

## Benefits

### Memory Optimization

- **Before:** All records loaded into memory at once
- **After:** Only current chunk in memory, with file caching for large datasets

### Performance

- **Before:** Single large query (potential timeout)
- **After:** Multiple smaller queries (faster execution)

### Reliability

- **Before:** Single point of failure
- **After:** Fault tolerance - continues even if one chunk fails

### Scalability

- **Before:** Fixed approach regardless of data size
- **After:** Adaptive strategy based on data volume

## Configuration Options

The chunking behavior can be customized by modifying the `getChunkingConfig()` method in `ChunkedServiceRequestsManager`:

```javascript
// Customize thresholds
if (estimatedTotalRecords > 50000) {
    return {
        monthsPerChunk: 1, // Smaller chunks for very large datasets
        totalMonths: 12, // 1 year of data (can be extended if needed)
        useFileCache: true,
    };
}
```

## Monitoring and Logging

The implementation provides detailed logging:

```
pnlcron debug starting chunked fetch: 4 chunks of 3 months each, useFileCache: false
pnlcron debug fetching chunk 1/4: 2024-10-10 to 2025-01-10
pnlcron debug chunk 1 fetched: 1250 records
pnlcron debug fetching chunk 2/4: 2024-07-10 to 2024-10-10
pnlcron debug chunk 2 fetched: 980 records
pnlcron debug fetching chunk 3/4: 2024-04-10 to 2024-07-10
pnlcron debug chunk 3 fetched: 875 records
pnlcron debug fetching chunk 4/4: 2024-01-10 to 2024-04-10
pnlcron debug chunk 4 fetched: 720 records
pnlcron debug chunked fetch completed: 3825 total records
```

## Testing

Run the test script to see the implementation in action:

```bash
node wify_tms_v2/backend/App/test_chunked_service_requests.js
```

## Backward Compatibility

The changes are fully backward compatible:

- Existing API calls work without modification
- The `getProfitAndLossByVertical()` method signature remains the same
- Database function accepts the same parameters as before (new parameters are optional)

## Future Enhancements

1. **Parallel Processing:** Process multiple chunks simultaneously
2. **Caching:** Add Redis caching for frequently accessed chunks
3. **Metrics:** Add performance metrics and monitoring
4. **Configuration:** Make chunk parameters configurable via environment variables
5. **Compression:** Compress temporary files for large datasets

## Usage Example

The chunked implementation is automatically used when calling:

```javascript
const services_model = new ServicesModel();
const result = await services_model.getProfitAndLossByVertical(
    orgId,
    verticalId,
    query
);
```

No changes needed in existing code - the chunking happens transparently behind the scenes.
