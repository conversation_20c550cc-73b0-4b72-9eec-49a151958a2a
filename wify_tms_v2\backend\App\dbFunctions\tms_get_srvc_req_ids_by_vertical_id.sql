-- DROP FUNCTION public.tms_get_srvc_req_ids_by_vertical_id(json);

CREATE OR REPLACE FUNCTION public.tms_get_srvc_req_ids_by_vertical_id(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$declare
    status boolean;
    message text;
    resp_data json;
    org_id_ integer;
    vertical_id_ integer;
    srvc_req_data json;
    start_date_ timestamp;
    end_date_ timestamp;

    begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';

    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    vertical_id_ := (form_data_->>'vertical_id')::integer;

    -- Extract optional date range parameters
    IF form_data_->>'start_date' IS NOT NULL THEN
        start_date_ := (form_data_->>'start_date')::timestamp;
    END IF;

    IF form_data_->>'end_date' IS NOT NULL THEN
        end_date_ := (form_data_->>'end_date')::timestamp;
    END IF;

    -- Get service requests for this vertical with optional date filtering
    SELECT array_to_json(array(
        SELECT jsonb_build_object(
            'srvc_req_id', srvc_req.db_id,
            'srvc_type_id', srvc_req.srvc_type_id,
            'display_code', srvc_req.display_code,
			'org_nick_name', org_.nickname,
			'creation_date', DATE((srvc_req.c_meta).time)
        )
      FROM cl_tx_srvc_req srvc_req
	  LEFT JOIN cl_tx_orgs as org_
		ON org_.org_id = srvc_req.org_id
     WHERE srvc_req.prvdr_vertical = vertical_id_
       AND srvc_req.srvc_prvdr = org_id_
       AND srvc_req.is_deleted is not true
       AND (start_date_ IS NULL OR (srvc_req.c_meta).time >= start_date_)
       AND (end_date_ IS NULL OR (srvc_req.c_meta).time <= end_date_)
     ORDER BY srvc_req.db_id DESC
    ))
    INTO srvc_req_data;
    -- Check if we found any service requests

    IF srvc_req_data IS NULL THEN
    status := true;
        message := 'no_service_requests_found';
        resp_data := '[]'::json;
    ELSE
        status := true;
        message := 'success';
        resp_data := srvc_req_data;
    END IF;
    -- Return the result
    RETURN json_build_object('status', status,'message', message, 'data', resp_data);
END;
$function$
;
