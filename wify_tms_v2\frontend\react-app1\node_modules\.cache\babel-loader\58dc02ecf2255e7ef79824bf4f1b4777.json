{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\routes\\\\setup\\\\srvc-req\\\\settings\\\\index.js\";\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Alert, Button, Form, Spin, Tabs, Tag, message } from 'antd';\nimport FormBuilder from 'antd-form-builder';\nimport CircularProgress from '../../../../components/CircularProgress';\nimport Widget from '../../../../components/Widget';\nimport http_utils from '../../../../util/http_utils';\nimport { handleClearSelect, validateLambdaArn } from '../../../../util/helpers';\nimport ConfigHelpers from '../../../../util/ConfigHelpers';\nimport { status_type_templates_ } from '../service-types/consumer-sms-notification-template-manager';\nimport { status_type_templates_fr_whatsapp } from '../service-types/consumer-whatsapp-notification-template-manager';\nimport { sbtsk_sms_templates_ } from '../sub-tasks-types/consumer-sms-notification-template-manager';\nimport { consumer_feedback_templates_fr_whatsapp } from '../service-types/consumer-whatsapp-feedback-template';\nimport CustomerSettings from './CustomerSettings';\nconst protoUrl = '/setup/settings/proto';\nconst submitUrl = '/setup/settings';\nconst Settings = () => {\n  const formRef = useRef();\n  const [renderHelper, setRenderHelper] = useState(false);\n  const [viewData, setViewData] = useState(undefined);\n  const [error, setError] = useState('');\n  const [isFormSubmitting, setIsFormSubmitting] = useState(false);\n  const [isLoadingViewData, setIsLoadingViewData] = useState(false);\n  const forceUpdate = FormBuilder.useForceUpdate();\n  const [showSmsTemplateCreationField, setShowSmsTemplateCreationField] = useState(false);\n  const [showWhatsAppTemplateCreationField, setShowWhatsAppTemplateCreationField] = useState(false);\n  const [tabName, setTabName] = useState(undefined);\n  const [subTabKeyFrCommunications, setSubTabKeyFrCommunications] = useState(undefined);\n  const [subTabKeyFrCustomWhatsAppTemplate, setSubTabKeyFrCustomWhatsAppTemplate] = useState(undefined);\n  const [subTabKeyFrCustomSmsTemplate, setSubTabKeyFrCustomSmsTemplate] = useState(undefined);\n  useEffect(() => {\n    initViewData();\n  }, []);\n  const initViewData = () => {\n    if (!isLoadingViewData) {\n      setIsLoadingViewData(true);\n      setViewData(undefined);\n      setError(undefined);\n      var params = {};\n      const onComplete = resp => {\n        setIsLoadingViewData(false);\n        setViewData(resp.data);\n        setTimeout(() => {\n          forceUpdate();\n        }, 200);\n      };\n      const onError = error => {\n        setIsLoadingViewData(false);\n        setError(http_utils.decodeErrorToMessage(error));\n      };\n      http_utils.performGetCall(protoUrl, params, onComplete, onError);\n    }\n  };\n  const getCommunicationMeta = () => {\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [{\n        key: 'lambda_voice_calling_numer',\n        widget: 'input',\n        label: 'Enter lambda arn for voice calling number',\n        tooltip: 'With this, you can define the custom number/api/provider that will be used for the calling feature on TMS.',\n        widgetProps: {\n          allowClear: true\n        },\n        rules: [{\n          validator: validateLambdaArn,\n          message: 'Please enter a valid Lambda ARN'\n        }]\n      }, {\n        key: 'lambda_fr_whatsapp_communication',\n        widget: 'input',\n        label: ' Enter lambda arn for WhatsApp communication',\n        tooltip: ' With this, you can define the custom number/api/provider that will be used for the whatsapp notifications being sent to the end consumer',\n        widgetProps: {\n          allowClear: true\n        },\n        rules: [{\n          validator: validateLambdaArn,\n          message: 'Please enter a valid Lambda ARN'\n        }]\n      }, {\n        key: 'lambda_fr_sms_communication',\n        widget: 'input',\n        label: 'Enter lambda arn for SMS communication',\n        tooltip: 'With this, you can define the custom number/api/provider that will be used for the SMS notifications being sent to the end consumer',\n        widgetProps: {\n          allowClear: true\n        },\n        rules: [{\n          validator: validateLambdaArn,\n          message: 'Please enter a valid Lambda ARN'\n        }]\n      }]\n    };\n    return meta;\n  };\n  const getTimezonesWithCountries = () => {\n    let data = [];\n    viewData.timezones.map(singleTimeZone => {\n      data.push({\n        value: singleTimeZone.timezone,\n        label: `${singleTimeZone.country} - ${singleTimeZone.timezone}  -  (UTC${singleTimeZone.utc_offset})`\n      });\n    });\n    return data;\n  };\n  const getCountryPhoneCodes = () => {\n    let data = [];\n    viewData.country_code_and_mobile_digit.map(singleCountry => {\n      data.push({\n        value: singleCountry.phone_code,\n        label: `${singleCountry.country} (${singleCountry.country_code})  ${singleCountry.phone_code}`\n      });\n    });\n    return data;\n  };\n  const getCountryCodesWithCountries = () => {\n    let data = [];\n    viewData.pincodes.map(singleCountryCode => {\n      data.push({\n        value: singleCountryCode.country_code,\n        label: `${singleCountryCode.country} - ${singleCountryCode.country_code}`\n      });\n    });\n    return data;\n  };\n  const getPincodeLength = (countryCode, countriesArray) => {\n    const foundCountry = countriesArray.find(country => country.country_code === countryCode);\n    return foundCountry ? foundCountry.pincode_length : null;\n  };\n  const getRegionalMeta = () => {\n    return {\n      columns: 1,\n      formItemLayout: null,\n      fields: [{\n        key: 'select_org_timezone',\n        label: 'Select organization timezone',\n        widget: 'select',\n        options: getTimezonesWithCountries(),\n        widgetProps: {\n          // allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children',\n          onChange: value => {\n            handleClearSelect(value, formRef, `select_org_timezone`);\n          }\n        }\n      }, {\n        key: 'select_consumer_phone_number_country_code',\n        label: 'Select consumer phone number country code',\n        widget: 'select',\n        options: getCountryPhoneCodes(),\n        widgetProps: {\n          // allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        }\n      }, {\n        key: 'select_number_of_digits_for_the_phone_number',\n        label: 'Select number of digits for the phone number',\n        widget: 'number',\n        tooltip: 'The number of digits that will be considered for consumer phone number and user phone number.',\n        required: true,\n        widgetProps: {\n          // allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children',\n          rules: [{\n            type: 'number',\n            min: 7,\n            max: 15\n          }]\n        }\n      }, {\n        key: 'select_country_code',\n        label: 'Select organization country code for pincode',\n        widget: 'select',\n        options: getCountryCodesWithCountries(),\n        widgetProps: {\n          // allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children',\n          onChange: value => {\n            if (formRef) {\n              formRef.current.setFieldsValue({\n                selected_country_pincode_length: getPincodeLength(value, viewData === null || viewData === void 0 ? void 0 : viewData.pincodes)\n              });\n            }\n            handleClearSelect(value, formRef, `select_country_code`);\n          }\n        }\n      }, {\n        key: 'selected_country_pincode_length',\n        label: 'Organization pincode length',\n        widget: 'select',\n        disabled: true,\n        widgetProps: {\n          // allowClear: true,\n          optionFilterProp: 'children',\n          onChange: value => {\n            handleClearSelect(value, formRef, `selected_country_pincode_length`);\n          }\n        }\n      }]\n    };\n  };\n  const handleFinish = values => {\n    if (!isFormSubmitting) {\n      setIsFormSubmitting(true);\n      var params = values;\n      if (subTabKeyFrCommunications == 'sms-template' && (values === null || values === void 0 ? void 0 : values.status_type_fr_sms_template) && (values === null || values === void 0 ? void 0 : values[`template_fr_${values === null || values === void 0 ? void 0 : values.status_type_fr_sms_template}_value`])) {\n        const status_type = values === null || values === void 0 ? void 0 : values.status_type_fr_sms_template;\n        params['custom_sms_templates'] = getCustomSmsTemplate(values);\n        delete params[`template_fr_${status_type}_value`];\n        delete params[`template_fr_${status_type}_label`];\n      }\n      delete params['status_type_fr_sms_template'];\n      if (subTabKeyFrCommunications == 'whatsapp-template' && (values === null || values === void 0 ? void 0 : values.status_type_fr_whatsapp_template) && (values === null || values === void 0 ? void 0 : values[`whatsapp_template_fr_${values === null || values === void 0 ? void 0 : values.status_type_fr_whatsapp_template}_value`])) {\n        const status_type = values === null || values === void 0 ? void 0 : values.status_type_fr_whatsapp_template;\n        params['custom_whatsapp_templates'] = getCustomWhatsAppTemplate(values);\n        delete params[`whatsapp_template_fr_${status_type}_value`];\n        delete params[`whatsapp_template_fr_${status_type}_label`];\n        delete params[`whatsapp_template_url_fr_${status_type}`];\n        delete params[`whatsapp_template_token_fr_${status_type}`];\n        delete params[`whatsapp_template_name_key_fr_${status_type}`];\n      }\n      delete params['status_type_fr_whatsapp_template'];\n      if (subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' && (values === null || values === void 0 ? void 0 : values[`whatsapp_feedback_template_value`])) {\n        params['custom_whatsapp_feedback_templates'] = getWhatsAppFeedbackTemplate(values);\n        delete params[`whatsapp_feedback_template_value`];\n        delete params[`whatsapp_feedback_template_label`];\n        delete params[`whatsapp_feedback_template_name_key`];\n      }\n      if (subTabKeyFrCustomSmsTemplate == 'consumer-otp-template' && (values === null || values === void 0 ? void 0 : values[`consumer_otp_template_fr_otp_sms_value`])) {\n        params['consumer_otp_sms_templates'] = getConsumerOTPTemplate(values);\n        delete params[`consumer_otp_template_fr_otp_sms_value`];\n        delete params[`consumer_otp_template_fr_otp_sms_label`];\n      }\n      if (subTabKeyFrCustomSmsTemplate == 'sms-feedback-template' && (values === null || values === void 0 ? void 0 : values[`sms_feedback_template_value`])) {\n        params['custom_sms_feedback_templates'] = getSmsFeedbackTemplate(values);\n        delete params[`sms_feedback_template_value`];\n        delete params[`sms_feedback_template_label`];\n        delete params[`sms_feedback_template_name_key`];\n      }\n      console.log('params', params);\n      const onComplete = resp => {\n        resetCustomTemplateValues();\n        resetCustomWhatsAppTemplateValues();\n        resetWhatsAppFeedbackTemplateValues();\n        resetSmsFeedbackTemplateValues();\n        resetConsumerOTPTemplateValues();\n        setIsFormSubmitting(false);\n        message.success('Saved successfully');\n      };\n      const onError = error => {\n        setIsFormSubmitting(false);\n        message.error(http_utils.decodeErrorToMessage(error));\n      };\n      http_utils.performPostCall(submitUrl, params, onComplete, onError);\n    }\n  };\n  const prefillFormData = (viewData === null || viewData === void 0 ? void 0 : viewData.form_data) || {};\n  const resetCustomTemplateValues = () => {\n    var _formRef$current;\n    let statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current = formRef.current) === null || _formRef$current === void 0 ? void 0 : _formRef$current.getFieldValue('status_type_fr_sms_template');\n    const templateValueKey = `template_fr_${statusType}_value`;\n    const templateLabelKey = `template_fr_${statusType}_label`;\n    formRef.current.setFieldsValue({\n      status_type_fr_sms_template: undefined,\n      [templateValueKey]: undefined,\n      [templateLabelKey]: undefined\n    });\n    setShowSmsTemplateCreationField(false);\n  };\n  const resetCustomWhatsAppTemplateValues = () => {\n    var _formRef$current2;\n    let statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current2 = formRef.current) === null || _formRef$current2 === void 0 ? void 0 : _formRef$current2.getFieldValue('status_type_fr_whatsapp_template');\n    const templateValueKey = `whatsapp_template_fr_${statusType}_value`;\n    const templateLabelKey = `whatsapp_template_fr_${statusType}_label`;\n    const templateUrlKey = `whatsapp_template_url_fr_${statusType}`;\n    const templateTokenKey = `whatsapp_template_token_fr_${statusType}`;\n    const templateNameKey = `whatsapp_template_name_key_fr_${statusType}`;\n    formRef.current.setFieldsValue({\n      status_type_fr_whatsapp_template: undefined,\n      [templateValueKey]: undefined,\n      [templateLabelKey]: undefined,\n      [templateUrlKey]: undefined,\n      [templateTokenKey]: undefined,\n      [templateNameKey]: undefined\n    });\n    setShowWhatsAppTemplateCreationField(false);\n  };\n  const resetWhatsAppFeedbackTemplateValues = () => {\n    const templateValueKey = `whatsapp_feedback_template_value`;\n    const templateLabelKey = `whatsapp_feedback_template_label`;\n    const templateNameKey = `whatsapp_feedback_template_name_key`;\n    const templateTokenKey = `whatsapp_feedback_template_token`;\n    const templateUrlKey = `whatsapp_feedback_template_url`;\n    formRef.current.setFieldsValue({\n      [templateValueKey]: undefined,\n      [templateLabelKey]: undefined,\n      [templateNameKey]: undefined,\n      [templateTokenKey]: undefined,\n      [templateUrlKey]: undefined\n    });\n  };\n  const resetSmsFeedbackTemplateValues = () => {\n    const templateValueKey = `sms_feedback_template_value`;\n    const templateLabelKey = `sms_feedback_template_label`;\n    const templateNameKey = `sms_feedback_template_name_key`;\n    formRef.current.setFieldsValue({\n      [templateValueKey]: undefined,\n      [templateLabelKey]: undefined,\n      [templateNameKey]: undefined\n    });\n  };\n  const resetConsumerOTPTemplateValues = () => {\n    let statusType = 'otp_sms';\n    const templateValueKey = `consumer_otp_template_fr_${statusType}_value`;\n    const templateLabelKey = `consumer_otp_template_fr_${statusType}_label`;\n    formRef.current.setFieldsValue({\n      [templateValueKey]: undefined,\n      [templateLabelKey]: undefined\n    });\n  };\n  const onStatusTypeChange = (statusType, currentStatusType) => {\n    if (showSmsTemplateCreationField) {\n      setShowSmsTemplateCreationField(false);\n      const templateValueKey = `template_fr_${statusType}_value`;\n      const templateLabelKey = `template_fr_${statusType}_label`;\n      formRef.current.setFieldsValue({\n        [templateValueKey]: undefined,\n        [templateLabelKey]: undefined\n      });\n    }\n  };\n  const onWhatsAppTemplateStatusTypeChange = (statusType, currentStatusType) => {\n    if (showWhatsAppTemplateCreationField) {\n      setShowWhatsAppTemplateCreationField(false);\n      const templateValueKey = `whatsapp_template_fr_${statusType}_value`;\n      const templateLabelKey = `whatsapp_template_fr_${statusType}_label`;\n      const templateNameKey = `whatsapp_template_name_key_fr_${statusType}`;\n      formRef.current.setFieldsValue({\n        [templateValueKey]: undefined,\n        [templateLabelKey]: undefined,\n        [templateNameKey]: undefined\n      });\n    }\n  };\n\n  /**\r\n   * Extracts custom field metadata from provided template values and labels.\r\n   *\r\n   * @param {Object} params - The parameters object.\r\n   * @param {string} params.newCustomTemplateValue - The template string containing custom variables in `%CUSTOM_VAR_x%` format.\r\n   * @param {string} params.newCustomTemplateLabel - The template string containing custom labels in `<CUSTOM_VAR_x>` format.\r\n   * @returns {Array<{ key: string, label: string, required: boolean, rules: Array<{ max: number, message: string }> }>}\r\n   * An array of objects representing custom field metadata, each containing:\r\n   * - `key`: Extracted custom variable key.\r\n   * - `label`: Corresponding label from the template.\r\n   * - `required`: Always `true`.\r\n   * - `rules`: Validation rule limiting the maximum length to 30 characters.\r\n   */\n  const getCustomFieldsMeta = ({\n    newCustomTemplateValue,\n    newCustomTemplateLabel\n  }) => {\n    var _newCustomTemplateVal, _newCustomTemplateLab;\n    let customFieldsMeta = [];\n    const customVariableFrValue = newCustomTemplateValue === null || newCustomTemplateValue === void 0 ? void 0 : (_newCustomTemplateVal = newCustomTemplateValue.match(/%CUSTOM_VAR_([^%]+)%/g)) === null || _newCustomTemplateVal === void 0 ? void 0 : _newCustomTemplateVal.map(m => m.slice(1, -1));\n    const customVariableFrLabel = newCustomTemplateLabel === null || newCustomTemplateLabel === void 0 ? void 0 : (_newCustomTemplateLab = newCustomTemplateLabel.match(/<CUSTOM_VAR_[^>]+>/g)) === null || _newCustomTemplateLab === void 0 ? void 0 : _newCustomTemplateLab.map(m => m.slice(1, -1));\n    if (customVariableFrValue) {\n      customVariableFrValue.forEach((singleCustomVariableFrValue, index) => {\n        customFieldsMeta.push({\n          key: singleCustomVariableFrValue,\n          label: customVariableFrLabel === null || customVariableFrLabel === void 0 ? void 0 : customVariableFrLabel[index],\n          required: true,\n          rules: [{\n            max: 30,\n            message: 'max 30 character'\n          }]\n        });\n      });\n    }\n    return customFieldsMeta;\n  };\n  const getCustomSmsTemplate = values => {\n    var _viewData$form_data, _customSmsTemplates;\n    let customSmsTemplates = (viewData === null || viewData === void 0 ? void 0 : (_viewData$form_data = viewData.form_data) === null || _viewData$form_data === void 0 ? void 0 : _viewData$form_data.custom_sms_templates) || [];\n    const statusType = values === null || values === void 0 ? void 0 : values.status_type_fr_sms_template;\n    const customTemplatePrefix = `template_fr_${statusType}`;\n    const newCustomTemplateValue = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_value`];\n    const newCustomTemplateLabel = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_label`];\n    const statusTypeCustomTemplateObject = (_customSmsTemplates = customSmsTemplates) === null || _customSmsTemplates === void 0 ? void 0 : _customSmsTemplates.find(singleCustomTemplate => {\n      return singleCustomTemplate.value == statusType;\n    });\n    const customFieldsMeta = getCustomFieldsMeta({\n      newCustomTemplateValue,\n      newCustomTemplateLabel\n    });\n    if (statusTypeCustomTemplateObject) {\n      customSmsTemplates.forEach(singleCustomSmsTemplate => {\n        if (singleCustomSmsTemplate.value == statusType) {\n          singleCustomSmsTemplate['templates'] = [...singleCustomSmsTemplate['templates'], {\n            value: newCustomTemplateValue,\n            label: newCustomTemplateLabel,\n            custom_fields_meta: customFieldsMeta\n          }];\n        }\n      });\n    } else {\n      customSmsTemplates = [...customSmsTemplates, {\n        value: statusType,\n        label: statusTypeCustomTemplateObject === null || statusTypeCustomTemplateObject === void 0 ? void 0 : statusTypeCustomTemplateObject.label,\n        templates: [{\n          value: newCustomTemplateValue,\n          label: newCustomTemplateLabel,\n          custom_fields_meta: customFieldsMeta\n        }]\n      }];\n    }\n    return customSmsTemplates;\n  };\n  const getCustomWhatsAppTemplate = values => {\n    var _viewData$form_data2, _customWhatsAppTempla;\n    let customWhatsAppTemplates = (viewData === null || viewData === void 0 ? void 0 : (_viewData$form_data2 = viewData.form_data) === null || _viewData$form_data2 === void 0 ? void 0 : _viewData$form_data2.custom_whatsapp_templates) || [];\n    const statusType = values === null || values === void 0 ? void 0 : values.status_type_fr_whatsapp_template;\n    const customTemplatePrefix = `whatsapp_template_fr_${statusType}`;\n    const customTemplateUrlfix = `whatsapp_template_url_fr_${statusType}`;\n    const customTemplateTokenfix = `whatsapp_template_token_fr_${statusType}`;\n    const newCustomTemplateNameKey = values === null || values === void 0 ? void 0 : values[`whatsapp_template_name_key_fr_${statusType}`];\n    const newCustomTemplateValue = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_value`];\n    const newCustomTemplateLabel = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_label`];\n    const newCustomTemplateUrl = values === null || values === void 0 ? void 0 : values[customTemplateUrlfix];\n    const newCustomTemplateToken = values === null || values === void 0 ? void 0 : values[customTemplateTokenfix];\n    const statusTypeCustomTemplateObject = (_customWhatsAppTempla = customWhatsAppTemplates) === null || _customWhatsAppTempla === void 0 ? void 0 : _customWhatsAppTempla.find(singleCustomTemplate => {\n      return singleCustomTemplate.value == statusType;\n    });\n    const tempVariableFrValue = newCustomTemplateValue.match(/%([^%]+)%/g);\n    const customFieldsMeta = getCustomFieldsMeta({\n      newCustomTemplateValue,\n      newCustomTemplateLabel\n    });\n    if (statusTypeCustomTemplateObject) {\n      customWhatsAppTemplates.forEach(singleCustomSmsTemplate => {\n        if (singleCustomSmsTemplate.value == statusType) {\n          singleCustomSmsTemplate['templates'] = [...singleCustomSmsTemplate['templates'], {\n            value: newCustomTemplateValue,\n            label: newCustomTemplateLabel,\n            variables_fr_api: getKeyValuePairFrWhatsAppTemplate(tempVariableFrValue),\n            url: newCustomTemplateUrl,\n            token: newCustomTemplateToken,\n            template_name: newCustomTemplateNameKey,\n            custom_fields_meta: customFieldsMeta\n          }];\n        }\n      });\n    } else {\n      customWhatsAppTemplates = [...customWhatsAppTemplates, {\n        value: statusType,\n        label: statusTypeCustomTemplateObject === null || statusTypeCustomTemplateObject === void 0 ? void 0 : statusTypeCustomTemplateObject.label,\n        templates: [{\n          value: newCustomTemplateValue,\n          label: newCustomTemplateLabel,\n          variables_fr_api: getKeyValuePairFrWhatsAppTemplate(tempVariableFrValue),\n          url: newCustomTemplateUrl,\n          token: newCustomTemplateToken,\n          template_name: newCustomTemplateNameKey,\n          custom_fields_meta: customFieldsMeta\n        }]\n      }];\n    }\n    return customWhatsAppTemplates;\n  };\n  const getWhatsAppFeedbackTemplate = values => {\n    var _viewData$form_data3;\n    let CustomWhatsAppFeedbackTemplates = (viewData === null || viewData === void 0 ? void 0 : (_viewData$form_data3 = viewData.form_data) === null || _viewData$form_data3 === void 0 ? void 0 : _viewData$form_data3.custom_whatsapp_feedback_templates) || [];\n    const customTemplatePrefix = `whatsapp_feedback_template`;\n    const newCustomTemplateValue = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_value`];\n    const newCustomTemplateLabel = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_label`];\n    const newCustomTemplateNameKey = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_name_key`];\n    const newCustomTemplateToken = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_token`];\n    const newCustomTemplateUrl = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_url`];\n    const tempVariableFrValue = newCustomTemplateValue.match(/%([^%]+)%/g);\n    CustomWhatsAppFeedbackTemplates = [...CustomWhatsAppFeedbackTemplates, {\n      value: newCustomTemplateValue,\n      label: newCustomTemplateLabel,\n      variables_fr_api: getKeyValuePairFrWhatsAppTemplate(tempVariableFrValue),\n      template_name: newCustomTemplateNameKey,\n      url: newCustomTemplateUrl,\n      token: newCustomTemplateToken\n    }];\n    return CustomWhatsAppFeedbackTemplates;\n  };\n  const getSmsFeedbackTemplate = values => {\n    var _viewData$form_data4;\n    let CustomSmsFeedbackTemplates = (viewData === null || viewData === void 0 ? void 0 : (_viewData$form_data4 = viewData.form_data) === null || _viewData$form_data4 === void 0 ? void 0 : _viewData$form_data4.custom_sms_feedback_templates) || [];\n    const customTemplatePrefix = `sms_feedback_template`;\n    const newCustomTemplateValue = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_value`];\n    const newCustomTemplateLabel = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_label`];\n    const newCustomTemplateNameKey = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_name_key`];\n    CustomSmsFeedbackTemplates = [...CustomSmsFeedbackTemplates, {\n      value: newCustomTemplateValue,\n      label: newCustomTemplateLabel,\n      template_name: newCustomTemplateNameKey\n    }];\n    return CustomSmsFeedbackTemplates;\n  };\n  const getConsumerOTPTemplate = values => {\n    var _viewData$form_data5, _ConsumerOtpSmsTempla;\n    let ConsumerOtpSmsTemplates = (viewData === null || viewData === void 0 ? void 0 : (_viewData$form_data5 = viewData.form_data) === null || _viewData$form_data5 === void 0 ? void 0 : _viewData$form_data5.consumer_otp_sms_templates) || [];\n    const statusType = 'otp_sms';\n    const customTemplatePrefix = `consumer_otp_template_fr_${statusType}`;\n    const newCustomTemplateValue = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_value`];\n    const newCustomTemplateLabel = values === null || values === void 0 ? void 0 : values[`${customTemplatePrefix}_label`];\n    const statusTypeCustomTemplateObject = (_ConsumerOtpSmsTempla = ConsumerOtpSmsTemplates) === null || _ConsumerOtpSmsTempla === void 0 ? void 0 : _ConsumerOtpSmsTempla.find(singleCustomTemplate => {\n      return singleCustomTemplate.value == statusType;\n    });\n    const tempVariableFrValue = newCustomTemplateValue.match(/%([^%]+)%/g);\n    if (statusTypeCustomTemplateObject) {\n      ConsumerOtpSmsTemplates.forEach(singleCustomSmsTemplate => {\n        if (singleCustomSmsTemplate.value == statusType) {\n          singleCustomSmsTemplate['templates'] = [...singleCustomSmsTemplate['templates'], {\n            value: newCustomTemplateValue,\n            label: newCustomTemplateLabel\n          }];\n        }\n      });\n    } else {\n      ConsumerOtpSmsTemplates = [...ConsumerOtpSmsTemplates, {\n        value: statusType,\n        label: statusTypeCustomTemplateObject === null || statusTypeCustomTemplateObject === void 0 ? void 0 : statusTypeCustomTemplateObject.label,\n        templates: [{\n          value: newCustomTemplateValue,\n          label: newCustomTemplateLabel\n        }]\n      }];\n    }\n    return ConsumerOtpSmsTemplates;\n  };\n  const getKeyValuePairFrWhatsAppTemplate = variables => {\n    const keyValuePairFrWhatsAppTemplate = {};\n    if ((variables === null || variables === void 0 ? void 0 : variables.length) > 0) {\n      variables.forEach((singleVariable, index) => {\n        keyValuePairFrWhatsAppTemplate[`parameters[${index}]`] = singleVariable;\n      });\n    }\n    return keyValuePairFrWhatsAppTemplate;\n  };\n  const statusTypeFrSmsTemplateApplyClick = () => {\n    var _formRef$current3;\n    if (formRef === null || formRef === void 0 ? void 0 : (_formRef$current3 = formRef.current) === null || _formRef$current3 === void 0 ? void 0 : _formRef$current3.getFieldValue('status_type_fr_sms_template')) {\n      setShowSmsTemplateCreationField(true);\n    } else {\n      message.info('Pls select status type');\n    }\n  };\n  const statusTypeWhatsAppTemplateApplyClick = () => {\n    var _formRef$current4;\n    if (formRef === null || formRef === void 0 ? void 0 : (_formRef$current4 = formRef.current) === null || _formRef$current4 === void 0 ? void 0 : _formRef$current4.getFieldValue('status_type_fr_whatsapp_template')) {\n      setShowWhatsAppTemplateCreationField(true);\n    } else {\n      message.info('Pls select status type');\n    }\n  };\n  const getStatusTypeFrCustomTemplateCreation = () => {\n    var _formRef$current5;\n    const statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current5 = formRef.current) === null || _formRef$current5 === void 0 ? void 0 : _formRef$current5.getFieldValue('status_type_fr_sms_template');\n    const defaultValueVariablesFrStatuses = {\n      schedule_assignment: ['%ext_order_id%', '%subtask_slot%', '%assignee_number%', '%display_code%'],\n      re_scheduled: ['%ext_order_id%'],\n      tx_re_assignment: ['%ext_order_id%']\n    };\n    const defaultLabelVariablesFrStatuses = {\n      schedule_assignment: ['<External ID>', '<Subtask Start and End time>', '<Assignee Number>', '<Request ID>'],\n      re_scheduled: ['<External ID>'],\n      tx_re_assignment: ['<External ID>']\n    };\n    const status_type_fr_custom_template_creation_data = status_type_templates_.map(singleStatusTypeTemplate => {\n      let availableVariablesFrValue = defaultValueVariablesFrStatuses[statusType] || [];\n      let availableVariablesFrLabel = defaultLabelVariablesFrStatuses[statusType] || [];\n      singleStatusTypeTemplate.templates.forEach(singleStatusTypeWiseTemplate => {\n        const templateValueString = singleStatusTypeWiseTemplate.value;\n        const templateLabelString = singleStatusTypeWiseTemplate.label;\n        const tempVariableFrValue = templateValueString.match(/%([^%]+)%/g);\n        const tempVariableFrLabel = templateLabelString.match(/<([^>]+)>/g);\n        if (tempVariableFrValue) {\n          const words = tempVariableFrValue.filter(match => !match.includes('CUSTOM_VAR'));\n          availableVariablesFrValue = [...new Set([...availableVariablesFrValue, ...words])];\n        }\n        if (tempVariableFrLabel) {\n          const words = tempVariableFrLabel.filter(match => !match.includes('custom_var'));\n          availableVariablesFrLabel = [...new Set([...availableVariablesFrLabel, ...words])];\n        }\n      });\n      return {\n        value: singleStatusTypeTemplate.value,\n        label: singleStatusTypeTemplate.label,\n        available_variables: availableVariablesFrValue,\n        available_label_variables: availableVariablesFrLabel\n      };\n    });\n    return status_type_fr_custom_template_creation_data;\n  };\n  const getStatusTypeFrCustomTemplateCreationFrWhatsApp = () => {\n    var _formRef$current6;\n    const statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current6 = formRef.current) === null || _formRef$current6 === void 0 ? void 0 : _formRef$current6.getFieldValue('status_type_fr_whatsapp_template');\n    const defaultValueVariablesFrStatuses = {\n      schedule_assignment: ['%ext_order_id%', '%assignee_number%', '%display_code%'],\n      re_scheduled: ['%ext_order_id%'],\n      tx_re_assignment: ['%ext_order_id%'],\n      request_closure: ['%link%']\n    };\n    const defaultLabelVariablesFrStatuses = {\n      schedule_assignment: ['<External ID>', '<Assignee Number>', '<Request ID>'],\n      re_scheduled: ['<External ID>'],\n      tx_re_assignment: ['<External ID>'],\n      request_closure: ['<link>']\n    };\n    const status_type_fr_custom_template_creation_data = status_type_templates_fr_whatsapp.map(singleStatusTypeTemplate => {\n      let availableVariablesFrValue = defaultValueVariablesFrStatuses[statusType] || [];\n      let availableVariablesFrLabel = defaultLabelVariablesFrStatuses[statusType] || [];\n      singleStatusTypeTemplate.templates.forEach(singleStatusTypeWiseTemplate => {\n        const templateValueString = singleStatusTypeWiseTemplate.value;\n        const templateLabelString = singleStatusTypeWiseTemplate.label;\n        const tempVariableFrValue = templateValueString.match(/%([^%]+)%/g);\n        const tempVariableFrLabel = templateLabelString.match(/<([^>]+)>/g);\n        if (tempVariableFrValue) {\n          const words = tempVariableFrValue.filter(match => !match.includes('CUSTOM_VAR'));\n          availableVariablesFrValue = [...new Set([...availableVariablesFrValue, ...words])];\n        }\n        if (tempVariableFrLabel) {\n          const words = tempVariableFrLabel.filter(match => !match.includes('custom_var'));\n          availableVariablesFrLabel = [...new Set([...availableVariablesFrLabel, ...words])];\n        }\n      });\n      return {\n        value: singleStatusTypeTemplate.value,\n        label: singleStatusTypeTemplate.label,\n        available_variables: availableVariablesFrValue,\n        available_label_variables: availableVariablesFrLabel\n      };\n    });\n    return status_type_fr_custom_template_creation_data;\n  };\n  const getWhatsAppFeedbackTemplateCreation = () => {\n    const custom_Whatsapp_feedback_template_creation_data = consumer_feedback_templates_fr_whatsapp.map(singleStatusTypeTemplate => {\n      let availableVariablesFrValue = [];\n      let availableVariablesFrLabel = [];\n      const templateValueString = singleStatusTypeTemplate.value;\n      const templateLabelString = singleStatusTypeTemplate.label;\n      const tempVariableFrValue = templateValueString.match(/%([^%]+)%/g);\n      const tempVariableFrLabel = templateLabelString.match(/<([^>]+)>/g);\n      if (tempVariableFrValue) {\n        const words = tempVariableFrValue.filter(match => !match.includes('CUSTOM_VAR'));\n        availableVariablesFrValue = [...new Set([...availableVariablesFrValue, ...words])];\n      }\n      if (tempVariableFrLabel) {\n        const words = tempVariableFrLabel.filter(match => !match.includes('custom_var'));\n        availableVariablesFrLabel = [...new Set([...availableVariablesFrLabel, ...words])];\n      }\n      return {\n        available_variables: availableVariablesFrValue,\n        available_label_variables: availableVariablesFrLabel\n      };\n    });\n    return custom_Whatsapp_feedback_template_creation_data;\n  };\n  const getStatusTypeFrConsumerOTPTemplateCreation = () => {\n    const custom_sbtsk_sms_template_creation_data = sbtsk_sms_templates_.map(singleStatusTypeTemplate => {\n      let availableVariablesFrValue = [];\n      let availableVariablesFrLabel = [];\n      singleStatusTypeTemplate.templates.forEach(singleStatusTypeWiseTemplate => {\n        const templateValueString = singleStatusTypeWiseTemplate.value;\n        const templateLabelString = singleStatusTypeWiseTemplate.label;\n        const tempVariableFrValue = templateValueString.match(/%([^%]+)%/g);\n        const tempVariableFrLabel = templateLabelString.match(/<([^>]+)>/g);\n        if (tempVariableFrValue) {\n          const words = tempVariableFrValue.filter(match => !match.includes('CUSTOM_VAR'));\n          availableVariablesFrValue = [...new Set([...availableVariablesFrValue, ...words])];\n        }\n        if (tempVariableFrLabel) {\n          const words = tempVariableFrLabel.filter(match => !match.includes('custom_var'));\n          availableVariablesFrLabel = [...new Set([...availableVariablesFrLabel, ...words])];\n        }\n      });\n      return {\n        value: singleStatusTypeTemplate.value,\n        label: singleStatusTypeTemplate.label,\n        available_variables: availableVariablesFrValue,\n        available_label_variables: availableVariablesFrLabel\n      };\n    });\n    return custom_sbtsk_sms_template_creation_data;\n  };\n  const validateCustomTemplateValueOrLabel = (rule, value, callback) => {\n    const params = rule.params;\n    const checkFrValue = rule.checkFrValue;\n    const checkFrLabel = rule.checkFrLabel;\n    let validFlag = true;\n    let tempVariableFrValueOrLabel;\n    if (checkFrValue) {\n      tempVariableFrValueOrLabel = value === null || value === void 0 ? void 0 : value.match(/%([^%]+)%/g);\n      if (subTabKeyFrCommunications == 'sms-template' && subTabKeyFrCustomSmsTemplate == undefined || subTabKeyFrCustomSmsTemplate == 'add-sms-template' || subTabKeyFrCommunications == 'whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') {\n        var _tempVariableFrValueO;\n        tempVariableFrValueOrLabel = (_tempVariableFrValueO = tempVariableFrValueOrLabel) === null || _tempVariableFrValueO === void 0 ? void 0 : _tempVariableFrValueO.filter(singleValue => !/^%CUSTOM_VAR_\\d+%$/.test(singleValue) // Exclude %CUSTOM_VAR_1%, %CUSTOM_VAR_2%, etc.\n        );\n      }\n    }\n    if (checkFrLabel) {\n      if (subTabKeyFrCommunications == 'sms-template' && subTabKeyFrCustomSmsTemplate == undefined || subTabKeyFrCustomSmsTemplate == 'add-sms-template' || subTabKeyFrCommunications == 'whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') {\n        tempVariableFrValueOrLabel = value === null || value === void 0 ? void 0 : value.match(/<(?!CUSTOM_VAR_)([^>]+)>/g);\n      } else {\n        tempVariableFrValueOrLabel = value === null || value === void 0 ? void 0 : value.match(/<([^>]+)>/g);\n      }\n    }\n    // Check if any element in the array is present in the string\n    if (tempVariableFrValueOrLabel) {\n      validFlag = !tempVariableFrValueOrLabel.some(element => !params.includes(element));\n    }\n    if (validFlag) {\n      callback(); // Validation passed\n    } else {\n      callback('Cannot save template, invalid variable values or labels'); // Validation failed\n    }\n  };\n  const getSmsTemplateCreationMeta = () => {\n    var _formRef$current7, _formRef$current9, _formRef$current0, _formRef$current1, _formRef$current10;\n    let status_type_fr_custom_template_creation = getStatusTypeFrCustomTemplateCreation();\n    const statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current7 = formRef.current) === null || _formRef$current7 === void 0 ? void 0 : _formRef$current7.getFieldValue('status_type_fr_sms_template');\n    const selected_status_type_value_and_label = status_type_fr_custom_template_creation === null || status_type_fr_custom_template_creation === void 0 ? void 0 : status_type_fr_custom_template_creation.find(singleStatusTypeCustomTemplate => {\n      var _formRef$current8;\n      return singleStatusTypeCustomTemplate.value == (formRef === null || formRef === void 0 ? void 0 : (_formRef$current8 = formRef.current) === null || _formRef$current8 === void 0 ? void 0 : _formRef$current8.getFieldValue('status_type_fr_sms_template'));\n    });\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [\n      // {\n      //   key : 'template_creation_heading',\n      //   render : () => {\n      //     return (\n      //       <div className=\"gx-mb-3\">\n      //         <b>Add SMS template</b>\n      //       </div>\n      //     )\n      //   }\n      // },\n      ...(subTabKeyFrCommunications == 'sms-template' ? [{\n        key: `status_type_fr_sms_template`,\n        label: 'Status Type',\n        widget: 'select',\n        placeholder: 'Select status type',\n        onChange: value => {\n          onStatusTypeChange(statusType, value);\n          forceUpdate();\n        },\n        options: status_type_fr_custom_template_creation,\n        widgetProps: {\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        }\n      }] : []), {\n        key: 'apply',\n        render: () => {\n          return /*#__PURE__*/React.createElement(Button, {\n            onClick: statusTypeFrSmsTemplateApplyClick,\n            type: \"primary\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1094,\n              columnNumber: 29\n            }\n          }, \"apply\");\n        }\n      }, ...(subTabKeyFrCommunications == 'sms-template' && (subTabKeyFrCustomSmsTemplate == undefined || subTabKeyFrCustomSmsTemplate == 'add-sms-template') && showSmsTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current9 = formRef.current) === null || _formRef$current9 === void 0 ? void 0 : _formRef$current9.getFieldValue('status_type_fr_sms_template')) ? [{\n        key: 'value_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 39\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1116,\n              columnNumber: 43\n            }\n          }), selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 51\n            }\n          }, reminder)));\n        }\n      }] : []), ...(subTabKeyFrCommunications == 'sms-template' && (subTabKeyFrCustomSmsTemplate == undefined || subTabKeyFrCustomSmsTemplate == 'add-sms-template') && showSmsTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current0 = formRef.current) === null || _formRef$current0 === void 0 ? void 0 : _formRef$current0.getFieldValue('status_type_fr_sms_template')) ? [{\n        key: `template_fr_${selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.value}_value`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1143,\n            columnNumber: 35\n          }\n        }, \"Add new template value for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1145,\n            columnNumber: 39\n          }\n        }, selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.label)),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_variables,\n          checkFrValue: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : []), ...(subTabKeyFrCommunications == 'sms-template' && (subTabKeyFrCustomSmsTemplate == undefined || subTabKeyFrCustomSmsTemplate == 'add-sms-template') && showSmsTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current1 = formRef.current) === null || _formRef$current1 === void 0 ? void 0 : _formRef$current1.getFieldValue('status_type_fr_sms_template')) ? [{\n        key: 'label_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1177,\n              columnNumber: 39\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1180,\n              columnNumber: 43\n            }\n          }), selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_label_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 51\n            }\n          }, reminder)));\n        }\n      }] : []), ...(subTabKeyFrCommunications == 'sms-template' && (subTabKeyFrCustomSmsTemplate == undefined || subTabKeyFrCustomSmsTemplate == 'add-sms-template') && showSmsTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current10 = formRef.current) === null || _formRef$current10 === void 0 ? void 0 : _formRef$current10.getFieldValue('status_type_fr_sms_template')) ? [{\n        key: `template_fr_${selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.value}_label`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1207,\n            columnNumber: 35\n          }\n        }, \"Add new template label for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1209,\n            columnNumber: 39\n          }\n        }, selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.label)),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_label_variables,\n          checkFrLabel: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : [])]\n    };\n    return meta;\n  };\n  const getWhatsAppTemplateCreationMeta = () => {\n    var _formRef$current11, _formRef$current13, _formRef$current14, _formRef$current15, _formRef$current16, _formRef$current17, _formRef$current18, _formRef$current19;\n    let status_type_fr_custom_template_creation = getStatusTypeFrCustomTemplateCreationFrWhatsApp();\n    const statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current11 = formRef.current) === null || _formRef$current11 === void 0 ? void 0 : _formRef$current11.getFieldValue('status_type_fr_whatsapp_template');\n    const selected_status_type_value_and_label = status_type_fr_custom_template_creation === null || status_type_fr_custom_template_creation === void 0 ? void 0 : status_type_fr_custom_template_creation.find(singleStatusTypeCustomTemplate => {\n      var _formRef$current12;\n      return singleStatusTypeCustomTemplate.value == (formRef === null || formRef === void 0 ? void 0 : (_formRef$current12 = formRef.current) === null || _formRef$current12 === void 0 ? void 0 : _formRef$current12.getFieldValue('status_type_fr_whatsapp_template'));\n    });\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [...(subTabKeyFrCommunications == 'whatsapp-template' ? [{\n        key: `status_type_fr_whatsapp_template`,\n        label: 'Status Type',\n        widget: 'select',\n        placeholder: 'Select status type',\n        onChange: value => {\n          onWhatsAppTemplateStatusTypeChange(statusType, value);\n          forceUpdate();\n        },\n        options: status_type_fr_custom_template_creation,\n        widgetProps: {\n          allowClear: true,\n          showSearch: true,\n          optionFilterProp: 'children'\n        }\n      }] : []), {\n        key: 'apply',\n        render: () => {\n          return /*#__PURE__*/React.createElement(Button, {\n            onClick: statusTypeWhatsAppTemplateApplyClick,\n            type: \"primary\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1284,\n              columnNumber: 29\n            }\n          }, \"apply\");\n        }\n      }, ...(subTabKeyFrCommunications == 'whatsapp-template' && (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && showWhatsAppTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current13 = formRef.current) === null || _formRef$current13 === void 0 ? void 0 : _formRef$current13.getFieldValue('status_type_fr_whatsapp_template')) ? [{\n        key: `whatsapp_template_name_key_fr_${selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.value}`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1305,\n            columnNumber: 35\n          }\n        }, \"Template Name for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1307,\n            columnNumber: 39\n          }\n        }, selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.label)),\n        onChange: value => forceUpdate()\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && showWhatsAppTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current14 = formRef.current) === null || _formRef$current14 === void 0 ? void 0 : _formRef$current14.getFieldValue('status_type_fr_whatsapp_template')) ? [{\n        key: 'whatsapp_value_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 39\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1334,\n              columnNumber: 43\n            }\n          }), selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1337,\n              columnNumber: 51\n            }\n          }, reminder)));\n        }\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && showWhatsAppTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current15 = formRef.current) === null || _formRef$current15 === void 0 ? void 0 : _formRef$current15.getFieldValue('status_type_fr_whatsapp_template')) ? [{\n        key: `whatsapp_template_fr_${selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.value}_value`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1364,\n            columnNumber: 35\n          }\n        }, \"Add new template value for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1366,\n            columnNumber: 39\n          }\n        }, selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.label)),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_variables,\n          checkFrValue: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && showWhatsAppTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current16 = formRef.current) === null || _formRef$current16 === void 0 ? void 0 : _formRef$current16.getFieldValue('status_type_fr_whatsapp_template')) ? [{\n        key: 'whatsapp_label_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1401,\n              columnNumber: 39\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1404,\n              columnNumber: 43\n            }\n          }), selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_label_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1407,\n              columnNumber: 51\n            }\n          }, reminder)));\n        }\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && showWhatsAppTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current17 = formRef.current) === null || _formRef$current17 === void 0 ? void 0 : _formRef$current17.getFieldValue('status_type_fr_whatsapp_template')) ? [{\n        key: `whatsapp_template_fr_${selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.value}_label`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1434,\n            columnNumber: 35\n          }\n        }, \"Add new template label for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1436,\n            columnNumber: 39\n          }\n        }, selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.label)),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.available_label_variables,\n          checkFrLabel: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && showWhatsAppTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current18 = formRef.current) === null || _formRef$current18 === void 0 ? void 0 : _formRef$current18.getFieldValue('status_type_fr_whatsapp_template')) ? [{\n        key: `whatsapp_template_url_fr_${selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.value}`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1470,\n            columnNumber: 35\n          }\n        }, \"Add url for new template\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1472,\n            columnNumber: 39\n          }\n        }, selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.label))\n        // formItemProps : {\n        //   style : {\n        //     display: 'none'\n        //   }\n        // },\n        // onChange: (value) => forceUpdate(),\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && showWhatsAppTemplateCreationField && (formRef === null || formRef === void 0 ? void 0 : (_formRef$current19 = formRef.current) === null || _formRef$current19 === void 0 ? void 0 : _formRef$current19.getFieldValue('status_type_fr_whatsapp_template')) ? [{\n        key: `whatsapp_template_token_fr_${selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.value}`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1500,\n            columnNumber: 35\n          }\n        }, \"Add token for new template\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1502,\n            columnNumber: 39\n          }\n        }, selected_status_type_value_and_label === null || selected_status_type_value_and_label === void 0 ? void 0 : selected_status_type_value_and_label.label))\n        // formItemProps : {\n        //   style : {\n        //     display: 'none'\n        //   }\n        // },\n        // onChange: (value) => forceUpdate(),\n      }] : [])]\n    };\n    return meta;\n  };\n  const getSmsFeedbackMessageTemplateCreationMeta = () => {\n    const defaultValueVariables = ['%cust_name%', '%display_code%', '%feedback_link%'];\n    const defaultLabelVariables = ['<customer name>', '<Req ID>', '<feedback_link>'];\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [...(subTabKeyFrCommunications == 'sms-template' && subTabKeyFrCustomSmsTemplate == 'sms-feedback-template' ? [{\n        key: `sms_feedback_template_name_key`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1543,\n            columnNumber: 38\n          }\n        }, \"Template Name\"),\n        onChange: value => forceUpdate()\n      }] : []), {\n        key: 'sms_feedback_value_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1552,\n              columnNumber: 29\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1554,\n              columnNumber: 33\n            }\n          }), defaultValueVariables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1557,\n              columnNumber: 41\n            }\n          }, reminder)));\n        }\n      }, ...(subTabKeyFrCommunications == 'sms-template' && subTabKeyFrCustomSmsTemplate == 'sms-feedback-template' ? [{\n        key: `sms_feedback_template_value`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1576,\n            columnNumber: 35\n          }\n        }, \"Add new template value for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1578,\n            columnNumber: 39\n          }\n        }, \"Consumer Feedback\")),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: defaultValueVariables,\n          checkFrValue: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : []), {\n        key: 'sms_feedback_label_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1600,\n              columnNumber: 29\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1602,\n              columnNumber: 33\n            }\n          }), defaultLabelVariables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1605,\n              columnNumber: 41\n            }\n          }, reminder)));\n        }\n      }, ...(subTabKeyFrCommunications == 'sms-template' && subTabKeyFrCustomSmsTemplate == 'sms-feedback-template' ? [{\n        key: `sms_feedback_template_label`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1624,\n            columnNumber: 35\n          }\n        }, \"Add new template label for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1626,\n            columnNumber: 39\n          }\n        }, \"Consumer Feedback\")),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: defaultLabelVariables,\n          checkFrLabel: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : [])]\n    };\n    return meta;\n  };\n  const getWhatsAppFeedbackMessageTemplateCreationMeta = () => {\n    let status_type_fr_custom_template_creation = getWhatsAppFeedbackTemplateCreation();\n    const whatsapp_feedback_template_value_and_label = status_type_fr_custom_template_creation[0];\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [...(subTabKeyFrCommunications == 'whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' ? [{\n        key: `whatsapp_feedback_template_name_key`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1664,\n            columnNumber: 38\n          }\n        }, \"Template Name\"),\n        onChange: value => forceUpdate()\n      }] : []), {\n        key: 'whatsapp_feedback_value_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1673,\n              columnNumber: 29\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1675,\n              columnNumber: 33\n            }\n          }), whatsapp_feedback_template_value_and_label === null || whatsapp_feedback_template_value_and_label === void 0 ? void 0 : whatsapp_feedback_template_value_and_label.available_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1678,\n              columnNumber: 41\n            }\n          }, reminder)));\n        }\n      }, ...(subTabKeyFrCommunications == 'whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' ? [{\n        key: `whatsapp_feedback_template_value`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1697,\n            columnNumber: 35\n          }\n        }, \"Add new template value for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1699,\n            columnNumber: 39\n          }\n        }, \"Consumer Feedback\")),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: whatsapp_feedback_template_value_and_label === null || whatsapp_feedback_template_value_and_label === void 0 ? void 0 : whatsapp_feedback_template_value_and_label.available_variables,\n          checkFrValue: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : []), {\n        key: 'whatsapp_feedback_label_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1721,\n              columnNumber: 29\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1723,\n              columnNumber: 33\n            }\n          }), whatsapp_feedback_template_value_and_label === null || whatsapp_feedback_template_value_and_label === void 0 ? void 0 : whatsapp_feedback_template_value_and_label.available_label_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1726,\n              columnNumber: 41\n            }\n          }, reminder)));\n        }\n      }, ...(subTabKeyFrCommunications == 'whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' ? [{\n        key: `whatsapp_feedback_template_label`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1745,\n            columnNumber: 35\n          }\n        }, \"Add new template label for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1747,\n            columnNumber: 39\n          }\n        }, \"Consumer Feedback\")),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: whatsapp_feedback_template_value_and_label === null || whatsapp_feedback_template_value_and_label === void 0 ? void 0 : whatsapp_feedback_template_value_and_label.available_label_variables,\n          checkFrLabel: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' ? [{\n        key: `whatsapp_feedback_template_token`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1771,\n            columnNumber: 35\n          }\n        }, \"Add new template token for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1773,\n            columnNumber: 39\n          }\n        }, \"Consumer Feedback\")),\n        onChange: value => forceUpdate()\n      }] : []), ...(subTabKeyFrCommunications == 'whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' ? [{\n        key: `whatsapp_feedback_template_url`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1786,\n            columnNumber: 35\n          }\n        }, \"Add new template url for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1788,\n            columnNumber: 39\n          }\n        }, \"Consumer Feedback\")),\n        onChange: value => forceUpdate()\n      }] : [])]\n    };\n    return meta;\n  };\n  const getConsumerOtpTemplateCreationMeta = () => {\n    let status_type_fr_custom_template_creation = getStatusTypeFrConsumerOTPTemplateCreation();\n    const selected_otp_sms_value_and_label = status_type_fr_custom_template_creation === null || status_type_fr_custom_template_creation === void 0 ? void 0 : status_type_fr_custom_template_creation.find(singleStatusTypeCustomTemplate => {\n      return singleStatusTypeCustomTemplate.value == 'otp_sms';\n    });\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [{\n        key: 'consumer_otp_value_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1818,\n              columnNumber: 29\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1820,\n              columnNumber: 33\n            }\n          }), selected_otp_sms_value_and_label === null || selected_otp_sms_value_and_label === void 0 ? void 0 : selected_otp_sms_value_and_label.available_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1823,\n              columnNumber: 41\n            }\n          }, reminder)));\n        }\n      }, ...(subTabKeyFrCommunications == 'sms-template' && subTabKeyFrCustomSmsTemplate == 'consumer-otp-template' ? [{\n        key: `consumer_otp_template_fr_${selected_otp_sms_value_and_label === null || selected_otp_sms_value_and_label === void 0 ? void 0 : selected_otp_sms_value_and_label.value}_value`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1842,\n            columnNumber: 35\n          }\n        }, \"Add new template value for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1844,\n            columnNumber: 39\n          }\n        }, \"Consumer OTP\")),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: selected_otp_sms_value_and_label === null || selected_otp_sms_value_and_label === void 0 ? void 0 : selected_otp_sms_value_and_label.available_variables,\n          checkFrValue: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : []), {\n        key: 'consumer_otp_label_varaibles',\n        render: () => {\n          return /*#__PURE__*/React.createElement(\"div\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1866,\n              columnNumber: 29\n            }\n          }, \"Use below variables, to create new template\", /*#__PURE__*/React.createElement(\"br\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1868,\n              columnNumber: 33\n            }\n          }), selected_otp_sms_value_and_label === null || selected_otp_sms_value_and_label === void 0 ? void 0 : selected_otp_sms_value_and_label.available_label_variables.map((reminder, index) => /*#__PURE__*/React.createElement(Tag, {\n            key: index,\n            color: \"orange\",\n            className: \"highlighted-tag\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1871,\n              columnNumber: 41\n            }\n          }, reminder)));\n        }\n      }, ...(subTabKeyFrCommunications == 'sms-template' && subTabKeyFrCustomSmsTemplate == 'consumer-otp-template' ? [{\n        key: `consumer_otp_template_fr_${selected_otp_sms_value_and_label === null || selected_otp_sms_value_and_label === void 0 ? void 0 : selected_otp_sms_value_and_label.value}_label`,\n        label: /*#__PURE__*/React.createElement(\"span\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1890,\n            columnNumber: 35\n          }\n        }, \"Add new template label for\", ' ', /*#__PURE__*/React.createElement(\"b\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1892,\n            columnNumber: 39\n          }\n        }, \"Consumer OTP\")),\n        required: true,\n        onChange: value => forceUpdate(),\n        rules: [{\n          validator: validateCustomTemplateValueOrLabel,\n          params: selected_otp_sms_value_and_label === null || selected_otp_sms_value_and_label === void 0 ? void 0 : selected_otp_sms_value_and_label.available_label_variables,\n          checkFrLabel: true,\n          message: 'Cannot save template, invalid variable values or labels'\n        }]\n      }] : [])]\n    };\n    return meta;\n  };\n  const setCurrentSubTabFrCommunications = tabNmae => {\n    setSubTabKeyFrCommunications(tabNmae);\n  };\n  const setCurrentSubTabFrCustomWhatsAppTemplate = tabNmae => {\n    setSubTabKeyFrCustomWhatsAppTemplate(tabNmae);\n  };\n  const setCurrentSubTabFrCustomSmsTemplate = tabNmae => {\n    setSubTabKeyFrCustomSmsTemplate(tabNmae);\n  };\n  const setCurrentTabNmae = tabNmae => {\n    setTabName(tabNmae);\n  };\n  const readyToSubmitSmsTemplateCreation = () => {\n    var _formRef$current20, _formRef$current21, _formRef$current22;\n    let statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current20 = formRef.current) === null || _formRef$current20 === void 0 ? void 0 : _formRef$current20.getFieldValue('status_type_fr_sms_template');\n    const templateValueKey = `template_fr_${statusType}_value`;\n    const templateLabelKey = `template_fr_${statusType}_label`;\n    const templateValue = formRef === null || formRef === void 0 ? void 0 : (_formRef$current21 = formRef.current) === null || _formRef$current21 === void 0 ? void 0 : _formRef$current21.getFieldValue(templateValueKey);\n    const templateLabel = formRef === null || formRef === void 0 ? void 0 : (_formRef$current22 = formRef.current) === null || _formRef$current22 === void 0 ? void 0 : _formRef$current22.getFieldValue(templateLabelKey);\n    return (subTabKeyFrCustomSmsTemplate == undefined || subTabKeyFrCustomSmsTemplate == 'add-sms-template') && subTabKeyFrCommunications == 'sms-template' && templateValue && templateLabel ? true : false;\n  };\n  const readyToSubmitWhatsAppsTemplateCreation = () => {\n    var _formRef$current23, _formRef$current24, _formRef$current25;\n    let statusType = formRef === null || formRef === void 0 ? void 0 : (_formRef$current23 = formRef.current) === null || _formRef$current23 === void 0 ? void 0 : _formRef$current23.getFieldValue('status_type_fr_whatsapp_template');\n    const templateValueKey = `whatsapp_template_fr_${statusType}_value`;\n    const templateLabelKey = `whatsapp_template_fr_${statusType}_label`;\n    // const templateNameKey = `whatsapp_template_name_key_fr_${statusType}`;\n    const templateValue = formRef === null || formRef === void 0 ? void 0 : (_formRef$current24 = formRef.current) === null || _formRef$current24 === void 0 ? void 0 : _formRef$current24.getFieldValue(templateValueKey);\n    const templateLabel = formRef === null || formRef === void 0 ? void 0 : (_formRef$current25 = formRef.current) === null || _formRef$current25 === void 0 ? void 0 : _formRef$current25.getFieldValue(templateLabelKey);\n    // const templateName = formRef?.current?.getFieldValue(templateNameKey)\n    return (subTabKeyFrCustomWhatsAppTemplate == undefined || subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') && subTabKeyFrCommunications != 'sms-template' && templateValue && templateLabel ? true : false;\n  };\n  const readyToSubmitWhatsAppFeedbackTemplateCreation = () => {\n    var _formRef$current26, _formRef$current27;\n    const templateValueKey = `whatsapp_feedback_template_value`;\n    const templateLabelKey = `whatsapp_feedback_template_label`;\n    // const templateNameKey = `whatsapp_feedback_template_name_key`;\n    const templateValue = formRef === null || formRef === void 0 ? void 0 : (_formRef$current26 = formRef.current) === null || _formRef$current26 === void 0 ? void 0 : _formRef$current26.getFieldValue(templateValueKey);\n    const templateLabel = formRef === null || formRef === void 0 ? void 0 : (_formRef$current27 = formRef.current) === null || _formRef$current27 === void 0 ? void 0 : _formRef$current27.getFieldValue(templateLabelKey);\n    // const templateName = formRef?.current?.getFieldValue(templateNameKey);\n    return subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' && subTabKeyFrCommunications == 'whatsapp-template' && templateValue && templateLabel ? true : false;\n  };\n  const readyToSubmitSmsFeedbackTemplateCreation = () => {\n    var _formRef$current28, _formRef$current29;\n    const templateValueKey = `sms_feedback_template_value`;\n    const templateLabelKey = `sms_feedback_template_label`;\n    // const templateNameKey = `whatsapp_feedback_template_name_key`;\n    const templateValue = formRef === null || formRef === void 0 ? void 0 : (_formRef$current28 = formRef.current) === null || _formRef$current28 === void 0 ? void 0 : _formRef$current28.getFieldValue(templateValueKey);\n    const templateLabel = formRef === null || formRef === void 0 ? void 0 : (_formRef$current29 = formRef.current) === null || _formRef$current29 === void 0 ? void 0 : _formRef$current29.getFieldValue(templateLabelKey);\n    // const templateName = formRef?.current?.getFieldValue(templateNameKey);\n    console.log('templateValue', templateValue);\n    console.log('templateLabel', templateLabel);\n    console.log('subTabKeyFrCustomSmsTemplate', subTabKeyFrCustomSmsTemplate);\n    console.log('subTabKeyFrCommunications', subTabKeyFrCommunications);\n    return subTabKeyFrCustomSmsTemplate == 'sms-feedback-template' && subTabKeyFrCommunications == 'sms-template' && templateValue && templateLabel ? true : false;\n  };\n  const readyToSubmitConsumerOTPTemplateCreation = () => {\n    var _formRef$current30, _formRef$current31;\n    let statusType = 'otp_sms';\n    const templateValueKey = `consumer_otp_template_fr_${statusType}_value`;\n    const templateLabelKey = `consumer_otp_template_fr_${statusType}_label`;\n    const templateValue = formRef === null || formRef === void 0 ? void 0 : (_formRef$current30 = formRef.current) === null || _formRef$current30 === void 0 ? void 0 : _formRef$current30.getFieldValue(templateValueKey);\n    const templateLabel = formRef === null || formRef === void 0 ? void 0 : (_formRef$current31 = formRef.current) === null || _formRef$current31 === void 0 ? void 0 : _formRef$current31.getFieldValue(templateLabelKey);\n    return subTabKeyFrCustomSmsTemplate == 'consumer-otp-template' && subTabKeyFrCommunications == 'sms-template' && templateValue && templateLabel ? true : false;\n  };\n  console.log('readyToSubmitSmsFeedbackTemplateCreation', readyToSubmitSmsFeedbackTemplateCreation());\n  return /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2020,\n      columnNumber: 9\n    }\n  }, isLoadingViewData ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-loader-view gx-loader-position\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2022,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(CircularProgress, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2023,\n      columnNumber: 21\n    }\n  })) : viewData == undefined ? /*#__PURE__*/React.createElement(\"p\", {\n    className: \"gx-text-red\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2026,\n      columnNumber: 17\n    }\n  }, error) : /*#__PURE__*/React.createElement(Widget, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2028,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Form, {\n    className: \"gx-mt-3\",\n    layout: \"vertical\",\n    ref: formRef,\n    onFinish: handleFinish,\n    initialValues: prefillFormData,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2030,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(Tabs, {\n    defaultActiveKey: \"regional\",\n    onChange: activeKey => setCurrentTabNmae(activeKey),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2037,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2044,\n        columnNumber: 42\n      }\n    }, \"Regional\"),\n    key: \"regional\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2043,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Alert, {\n    message: \"This setting is used to select the timezone that will be considered for subtask creation, rescheduling for this organization.\",\n    description: \"Note: In case no selection is done, Indian Standard Time is considered by default.\",\n    type: \"info\",\n    showIcon: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2048,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(FormBuilder, {\n    meta: getRegionalMeta(),\n    form: formRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2054,\n      columnNumber: 37\n    }\n  })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2061,\n        columnNumber: 42\n      }\n    }, \"Communications\"),\n    key: \"communication\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2060,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Tabs, {\n    defaultActiveKey: \"lambda\",\n    onChange: activeKey => setCurrentSubTabFrCommunications(activeKey),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2065,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"Lambda\",\n    key: \"lambda\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2073,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Alert\n  // className=\"gx-my-1 gx-mt-2\"\n  , {\n    message: \"About communications...\",\n    description: \"If you don't know what lambda means, leave the menu right away! This section helps you configure custom phone numbers/apis/providers that will be used for communication via WhatsApp, SMS and Voice calling\",\n    type: \"info\",\n    showIcon: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2078,\n      columnNumber: 45\n    }\n  }), /*#__PURE__*/React.createElement(FormBuilder, {\n    meta: getCommunicationMeta(),\n    form: formRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2085,\n      columnNumber: 45\n    }\n  })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"SMS Template\",\n    key: \"sms-template\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2090,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Tabs, {\n    defaultActiveKey: \"add-sms-template\",\n    onChange: activeKey => setCurrentSubTabFrCustomSmsTemplate(activeKey),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2095,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"Add SMS Template\",\n    key: \"add-sms-template\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2103,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(FormBuilder, {\n    form: formRef,\n    meta: getSmsTemplateCreationMeta(),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2109,\n      columnNumber: 53\n    }\n  })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"Consumer OTP Template\",\n    key: \"consumer-otp-template\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2114,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(FormBuilder, {\n    form: formRef,\n    meta: getConsumerOtpTemplateCreationMeta(),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2120,\n      columnNumber: 53\n    }\n  })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"SMS Feedback Template\",\n    key: \"sms-feedback-template\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2125,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(FormBuilder, {\n    form: formRef,\n    meta: getSmsFeedbackMessageTemplateCreationMeta(),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2131,\n      columnNumber: 53\n    }\n  })))), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"WhatsApp Template\",\n    key: \"whatsapp-template\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2138,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(Tabs, {\n    defaultActiveKey: \"add-whatsapp-template\",\n    onChange: activeKey => setCurrentSubTabFrCustomWhatsAppTemplate(activeKey),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2143,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"Status Wise Template\",\n    key: \"add-whatsapp-template\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2151,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(FormBuilder, {\n    form: formRef,\n    meta: getWhatsAppTemplateCreationMeta(),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2157,\n      columnNumber: 53\n    }\n  })), /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: \"Feedback Template\",\n    key: \"feedback-template\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2162,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(FormBuilder, {\n    form: formRef,\n    meta: getWhatsAppFeedbackMessageTemplateCreationMeta(),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2168,\n      columnNumber: 53\n    }\n  })))))), ConfigHelpers.isBrand() && /*#__PURE__*/React.createElement(Tabs.TabPane, {\n    tab: /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 2180,\n        columnNumber: 46\n      }\n    }, \"Customer Settings\"),\n    key: \"customer-settings\",\n    forceRender: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2179,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(CustomerSettings, {\n    formRef: formRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2184,\n      columnNumber: 41\n    }\n  }))), (tabName != 'communication' || tabName == 'customer-settings' || subTabKeyFrCommunications == undefined || subTabKeyFrCommunications == 'lambda' || subTabKeyFrCustomWhatsAppTemplate != undefined && subTabKeyFrCustomWhatsAppTemplate != 'add-whatsapp-template' && subTabKeyFrCustomWhatsAppTemplate != 'feedback-template' && subTabKeyFrCommunications != 'sms-template' && subTabKeyFrCustomSmsTemplate != undefined && subTabKeyFrCustomSmsTemplate != 'add-sms-template' && subTabKeyFrCustomSmsTemplate != 'consumer-otp-template' && subTabKeyFrCustomSmsTemplate != 'sms-feedback-template' && subTabKeyFrCommunications != 'whatsapp-template' || readyToSubmitSmsTemplateCreation() || readyToSubmitWhatsAppsTemplateCreation() || readyToSubmitWhatsAppFeedbackTemplateCreation() || readyToSubmitSmsFeedbackTemplateCreation() || readyToSubmitConsumerOTPTemplateCreation()) && /*#__PURE__*/React.createElement(Form.Item, {\n    className: \"form-footer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2214,\n      columnNumber: 33\n    }\n  }, isFormSubmitting && /*#__PURE__*/React.createElement(Spin, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2215,\n      columnNumber: 58\n    }\n  }), /*#__PURE__*/React.createElement(Button, {\n    htmlType: \"submit\",\n    type: \"primary\",\n    className: \"gx-mb-0\",\n    disabled: isFormSubmitting,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 2216,\n      columnNumber: 37\n    }\n  }, \"Submit\"))))));\n};\nexport default Settings;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Form", "Spin", "Tabs", "Tag", "message", "FormBuilder", "CircularProgress", "Widget", "http_utils", "handleClearSelect", "validateLambdaArn", "ConfigHelpers", "status_type_templates_", "status_type_templates_fr_whatsapp", "sbtsk_sms_templates_", "consumer_feedback_templates_fr_whatsapp", "CustomerSettings", "protoUrl", "submitUrl", "Settings", "formRef", "renderHelper", "set<PERSON><PERSON>Helper", "viewData", "setViewData", "undefined", "error", "setError", "isFormSubmitting", "setIsFormSubmitting", "isLoadingViewData", "setIsLoadingViewData", "forceUpdate", "useForceUpdate", "showSmsTemplateCreationField", "setShowSmsTemplateCreationField", "showWhatsAppTemplateCreationField", "setShowWhatsAppTemplateCreationField", "tabName", "setTabName", "subTabKeyFrCommunications", "setSubTabKeyFrCommunications", "subTabKeyFrCustomWhatsAppTemplate", "setSubTabKeyFrCustomWhatsAppTemplate", "subTabKeyFrCustomSmsTemplate", "setSubTabKeyFrCustomSmsTemplate", "initViewData", "params", "onComplete", "resp", "data", "setTimeout", "onError", "decodeErrorToMessage", "performGetCall", "getCommunicationMeta", "meta", "columns", "formItemLayout", "fields", "key", "widget", "label", "tooltip", "widgetProps", "allowClear", "rules", "validator", "getTimezonesWithCountries", "timezones", "map", "singleTimeZone", "push", "value", "timezone", "country", "utc_offset", "getCountryPhoneCodes", "country_code_and_mobile_digit", "singleCountry", "phone_code", "country_code", "getCountryCodesWithCountries", "pincodes", "singleCountryCode", "getPincodeLength", "countryCode", "countriesArray", "foundCountry", "find", "pincode_length", "getRegionalMeta", "options", "showSearch", "optionFilterProp", "onChange", "required", "type", "min", "max", "current", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selected_country_pincode_length", "disabled", "handleFinish", "values", "status_type_fr_sms_template", "status_type", "getCustomSmsTemplate", "status_type_fr_whatsapp_template", "getCustomWhatsAppTemplate", "getWhatsAppFeedbackTemplate", "getConsumerOTPTemplate", "getSmsFeedbackTemplate", "console", "log", "resetCustomTemplateValues", "resetCustomWhatsAppTemplateValues", "resetWhatsAppFeedbackTemplateValues", "resetSmsFeedbackTemplateValues", "resetConsumerOTPTemplateValues", "success", "performPostCall", "prefillFormData", "form_data", "_formRef$current", "statusType", "getFieldValue", "templateValueKey", "templateLabel<PERSON>ey", "_formRef$current2", "templateUrl<PERSON>ey", "templateTokenKey", "templateNameKey", "onStatusTypeChange", "currentStatusType", "onWhatsAppTemplateStatusTypeChange", "getCustomFieldsMeta", "newCustomTemplateValue", "newCustomTemplateLabel", "_newCustomTemplateVal", "_newCustomTemplateLab", "customFieldsMeta", "customVariableFrValue", "match", "m", "slice", "customVariableFrLabel", "for<PERSON>ach", "singleCustomVariableFrValue", "index", "_viewData$form_data", "_customSmsTemplates", "customSmsTemplates", "custom_sms_templates", "customTemplatePrefix", "statusTypeCustomTemplateObject", "singleCustomTemplate", "singleCustomSmsTemplate", "custom_fields_meta", "templates", "_viewData$form_data2", "_customWhatsAppTempla", "customWhatsAppTemplates", "custom_whatsapp_templates", "customTemplateUrlfix", "customTemplateTokenfix", "newCustomTemplateNameKey", "newCustomTemplateUrl", "newCustomTemplateToken", "tempVariableFrValue", "variables_fr_api", "getKeyValuePairFrWhatsAppTemplate", "url", "token", "template_name", "_viewData$form_data3", "CustomWhatsAppFeedbackTemplates", "custom_whatsapp_feedback_templates", "_viewData$form_data4", "CustomSmsFeedbackTemplates", "custom_sms_feedback_templates", "_viewData$form_data5", "_ConsumerOtpSmsTempla", "ConsumerOtpSmsTemplates", "consumer_otp_sms_templates", "variables", "keyValuePairFrWhatsAppTemplate", "length", "singleVariable", "statusTypeFrSmsTemplateApplyClick", "_formRef$current3", "info", "statusTypeWhatsAppTemplateApplyClick", "_formRef$current4", "getStatusTypeFrCustomTemplateCreation", "_formRef$current5", "defaultValueVariablesFrStatuses", "schedule_assignment", "re_scheduled", "tx_re_assignment", "defaultLabelVariablesFrStatuses", "status_type_fr_custom_template_creation_data", "singleStatusTypeTemplate", "availableVariablesFrValue", "availableVariablesFrLabel", "singleStatusTypeWiseTemplate", "templateValueString", "templateLabelString", "tempVariableFrLabel", "words", "filter", "includes", "Set", "available_variables", "available_label_variables", "getStatusTypeFrCustomTemplateCreationFrWhatsApp", "_formRef$current6", "request_closure", "getWhatsAppFeedbackTemplateCreation", "custom_Whatsapp_feedback_template_creation_data", "getStatusTypeFrConsumerOTPTemplateCreation", "custom_sbtsk_sms_template_creation_data", "validateCustomTemplateValueOrLabel", "rule", "callback", "checkFrValue", "checkFrLabel", "validFlag", "tempVariableFrValueOrLabel", "_tempVariableFrValueO", "singleValue", "test", "some", "element", "getSmsTemplateCreationMeta", "_formRef$current7", "_formRef$current9", "_formRef$current0", "_formRef$current1", "_formRef$current10", "status_type_fr_custom_template_creation", "selected_status_type_value_and_label", "singleStatusTypeCustomTemplate", "_formRef$current8", "placeholder", "render", "createElement", "onClick", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "reminder", "color", "className", "getWhatsAppTemplateCreationMeta", "_formRef$current11", "_formRef$current13", "_formRef$current14", "_formRef$current15", "_formRef$current16", "_formRef$current17", "_formRef$current18", "_formRef$current19", "_formRef$current12", "getSmsFeedbackMessageTemplateCreationMeta", "defaultValueVariables", "defaultLabelVariables", "getWhatsAppFeedbackMessageTemplateCreationMeta", "whatsapp_feedback_template_value_and_label", "getConsumerOtpTemplateCreationMeta", "selected_otp_sms_value_and_label", "setCurrentSubTabFrCommunications", "tabNmae", "setCurrentSubTabFrCustomWhatsAppTemplate", "setCurrentSubTabFrCustomSmsTemplate", "setCurrentTabNmae", "readyToSubmitSmsTemplateCreation", "_formRef$current20", "_formRef$current21", "_formRef$current22", "templateValue", "templateLabel", "readyToSubmitWhatsAppsTemplateCreation", "_formRef$current23", "_formRef$current24", "_formRef$current25", "readyToSubmitWhatsAppFeedbackTemplateCreation", "_formRef$current26", "_formRef$current27", "readyToSubmitSmsFeedbackTemplateCreation", "_formRef$current28", "_formRef$current29", "readyToSubmitConsumerOTPTemplateCreation", "_formRef$current30", "_formRef$current31", "Fragment", "layout", "ref", "onFinish", "initialValues", "defaultActiveKey", "active<PERSON><PERSON>", "TabPane", "tab", "forceRender", "description", "showIcon", "form", "isBrand", "<PERSON><PERSON>", "htmlType"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/routes/setup/srvc-req/settings/index.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\r\nimport { <PERSON><PERSON>, But<PERSON>, Form, Spin, Tabs, Tag, message } from 'antd';\r\nimport FormBuilder from 'antd-form-builder';\r\nimport CircularProgress from '../../../../components/CircularProgress';\r\nimport Widget from '../../../../components/Widget';\r\nimport http_utils from '../../../../util/http_utils';\r\nimport { handleClearSelect, validateLambdaArn } from '../../../../util/helpers';\r\nimport ConfigHelpers from '../../../../util/ConfigHelpers';\r\nimport { status_type_templates_ } from '../service-types/consumer-sms-notification-template-manager';\r\nimport { status_type_templates_fr_whatsapp } from '../service-types/consumer-whatsapp-notification-template-manager';\r\nimport { sbtsk_sms_templates_ } from '../sub-tasks-types/consumer-sms-notification-template-manager';\r\nimport { consumer_feedback_templates_fr_whatsapp } from '../service-types/consumer-whatsapp-feedback-template';\r\nimport CustomerSettings from './CustomerSettings';\r\n\r\nconst protoUrl = '/setup/settings/proto';\r\nconst submitUrl = '/setup/settings';\r\n\r\nconst Settings = () => {\r\n    const formRef = useRef();\r\n    const [renderHelper, setRenderHelper] = useState(false);\r\n    const [viewData, setViewData] = useState(undefined);\r\n    const [error, setError] = useState('');\r\n    const [isFormSubmitting, setIsFormSubmitting] = useState(false);\r\n    const [isLoadingViewData, setIsLoadingViewData] = useState(false);\r\n    const forceUpdate = FormBuilder.useForceUpdate();\r\n    const [showSmsTemplateCreationField, setShowSmsTemplateCreationField] =\r\n        useState(false);\r\n    const [\r\n        showWhatsAppTemplateCreationField,\r\n        setShowWhatsAppTemplateCreationField,\r\n    ] = useState(false);\r\n    const [tabName, setTabName] = useState(undefined);\r\n    const [subTabKeyFrCommunications, setSubTabKeyFrCommunications] =\r\n        useState(undefined);\r\n    const [\r\n        subTabKeyFrCustomWhatsAppTemplate,\r\n        setSubTabKeyFrCustomWhatsAppTemplate,\r\n    ] = useState(undefined);\r\n    const [subTabKeyFrCustomSmsTemplate, setSubTabKeyFrCustomSmsTemplate] =\r\n        useState(undefined);\r\n\r\n    useEffect(() => {\r\n        initViewData();\r\n    }, []);\r\n\r\n    const initViewData = () => {\r\n        if (!isLoadingViewData) {\r\n            setIsLoadingViewData(true);\r\n            setViewData(undefined);\r\n            setError(undefined);\r\n            var params = {};\r\n            const onComplete = (resp) => {\r\n                setIsLoadingViewData(false);\r\n                setViewData(resp.data);\r\n                setTimeout(() => {\r\n                    forceUpdate();\r\n                }, 200);\r\n            };\r\n            const onError = (error) => {\r\n                setIsLoadingViewData(false);\r\n                setError(http_utils.decodeErrorToMessage(error));\r\n            };\r\n            http_utils.performGetCall(protoUrl, params, onComplete, onError);\r\n        }\r\n    };\r\n\r\n    const getCommunicationMeta = () => {\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: 'lambda_voice_calling_numer',\r\n                    widget: 'input',\r\n                    label: 'Enter lambda arn for voice calling number',\r\n                    tooltip:\r\n                        'With this, you can define the custom number/api/provider that will be used for the calling feature on TMS.',\r\n                    widgetProps: {\r\n                        allowClear: true,\r\n                    },\r\n                    rules: [\r\n                        {\r\n                            validator: validateLambdaArn,\r\n                            message: 'Please enter a valid Lambda ARN',\r\n                        },\r\n                    ],\r\n                },\r\n                {\r\n                    key: 'lambda_fr_whatsapp_communication',\r\n                    widget: 'input',\r\n                    label: ' Enter lambda arn for WhatsApp communication',\r\n                    tooltip:\r\n                        ' With this, you can define the custom number/api/provider that will be used for the whatsapp notifications being sent to the end consumer',\r\n                    widgetProps: {\r\n                        allowClear: true,\r\n                    },\r\n                    rules: [\r\n                        {\r\n                            validator: validateLambdaArn,\r\n                            message: 'Please enter a valid Lambda ARN',\r\n                        },\r\n                    ],\r\n                },\r\n                {\r\n                    key: 'lambda_fr_sms_communication',\r\n                    widget: 'input',\r\n                    label: 'Enter lambda arn for SMS communication',\r\n                    tooltip:\r\n                        'With this, you can define the custom number/api/provider that will be used for the SMS notifications being sent to the end consumer',\r\n                    widgetProps: {\r\n                        allowClear: true,\r\n                    },\r\n                    rules: [\r\n                        {\r\n                            validator: validateLambdaArn,\r\n                            message: 'Please enter a valid Lambda ARN',\r\n                        },\r\n                    ],\r\n                },\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    const getTimezonesWithCountries = () => {\r\n        let data = [];\r\n        viewData.timezones.map((singleTimeZone) => {\r\n            data.push({\r\n                value: singleTimeZone.timezone,\r\n                label: `${singleTimeZone.country} - ${singleTimeZone.timezone}  -  (UTC${singleTimeZone.utc_offset})`,\r\n            });\r\n        });\r\n        return data;\r\n    };\r\n\r\n    const getCountryPhoneCodes = () => {\r\n        let data = [];\r\n        viewData.country_code_and_mobile_digit.map((singleCountry) => {\r\n            data.push({\r\n                value: singleCountry.phone_code,\r\n                label: `${singleCountry.country} (${singleCountry.country_code})  ${singleCountry.phone_code}`,\r\n            });\r\n        });\r\n        return data;\r\n    };\r\n\r\n    const getCountryCodesWithCountries = () => {\r\n        let data = [];\r\n        viewData.pincodes.map((singleCountryCode) => {\r\n            data.push({\r\n                value: singleCountryCode.country_code,\r\n                label: `${singleCountryCode.country} - ${singleCountryCode.country_code}`,\r\n            });\r\n        });\r\n        return data;\r\n    };\r\n\r\n    const getPincodeLength = (countryCode, countriesArray) => {\r\n        const foundCountry = countriesArray.find(\r\n            (country) => country.country_code === countryCode\r\n        );\r\n        return foundCountry ? foundCountry.pincode_length : null;\r\n    };\r\n\r\n    const getRegionalMeta = () => {\r\n        return {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: 'select_org_timezone',\r\n                    label: 'Select organization timezone',\r\n                    widget: 'select',\r\n                    options: getTimezonesWithCountries(),\r\n                    widgetProps: {\r\n                        // allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                        onChange: (value) => {\r\n                            handleClearSelect(\r\n                                value,\r\n                                formRef,\r\n                                `select_org_timezone`\r\n                            );\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    key: 'select_consumer_phone_number_country_code',\r\n                    label: 'Select consumer phone number country code',\r\n                    widget: 'select',\r\n                    options: getCountryPhoneCodes(),\r\n                    widgetProps: {\r\n                        // allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                    },\r\n                },\r\n                {\r\n                    key: 'select_number_of_digits_for_the_phone_number',\r\n                    label: 'Select number of digits for the phone number',\r\n                    widget: 'number',\r\n                    tooltip:\r\n                        'The number of digits that will be considered for consumer phone number and user phone number.',\r\n                    required: true,\r\n                    widgetProps: {\r\n                        // allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                        rules: [\r\n                            {\r\n                                type: 'number',\r\n                                min: 7,\r\n                                max: 15,\r\n                            },\r\n                        ],\r\n                    },\r\n                },\r\n                {\r\n                    key: 'select_country_code',\r\n                    label: 'Select organization country code for pincode',\r\n                    widget: 'select',\r\n                    options: getCountryCodesWithCountries(),\r\n                    widgetProps: {\r\n                        // allowClear: true,\r\n                        showSearch: true,\r\n                        optionFilterProp: 'children',\r\n                        onChange: (value) => {\r\n                            if (formRef) {\r\n                                formRef.current.setFieldsValue({\r\n                                    selected_country_pincode_length:\r\n                                        getPincodeLength(\r\n                                            value,\r\n                                            viewData?.pincodes\r\n                                        ),\r\n                                });\r\n                            }\r\n                            handleClearSelect(\r\n                                value,\r\n                                formRef,\r\n                                `select_country_code`\r\n                            );\r\n                        },\r\n                    },\r\n                },\r\n                {\r\n                    key: 'selected_country_pincode_length',\r\n                    label: 'Organization pincode length',\r\n                    widget: 'select',\r\n                    disabled: true,\r\n                    widgetProps: {\r\n                        // allowClear: true,\r\n                        optionFilterProp: 'children',\r\n                        onChange: (value) => {\r\n                            handleClearSelect(\r\n                                value,\r\n                                formRef,\r\n                                `selected_country_pincode_length`\r\n                            );\r\n                        },\r\n                    },\r\n                },\r\n            ],\r\n        };\r\n    };\r\n\r\n\r\n    const handleFinish = (values) => {\r\n        if (!isFormSubmitting) {\r\n            setIsFormSubmitting(true);\r\n            var params = values;\r\n            if (\r\n                subTabKeyFrCommunications == 'sms-template' &&\r\n                values?.status_type_fr_sms_template &&\r\n                values?.[\r\n                    `template_fr_${values?.status_type_fr_sms_template}_value`\r\n                ]\r\n            ) {\r\n                const status_type = values?.status_type_fr_sms_template;\r\n                params['custom_sms_templates'] = getCustomSmsTemplate(values);\r\n                delete params[`template_fr_${status_type}_value`];\r\n                delete params[`template_fr_${status_type}_label`];\r\n            }\r\n            delete params['status_type_fr_sms_template'];\r\n            if (\r\n                subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                values?.status_type_fr_whatsapp_template &&\r\n                values?.[\r\n                    `whatsapp_template_fr_${values?.status_type_fr_whatsapp_template}_value`\r\n                ]\r\n            ) {\r\n                const status_type = values?.status_type_fr_whatsapp_template;\r\n                params['custom_whatsapp_templates'] =\r\n                    getCustomWhatsAppTemplate(values);\r\n                delete params[`whatsapp_template_fr_${status_type}_value`];\r\n                delete params[`whatsapp_template_fr_${status_type}_label`];\r\n                delete params[`whatsapp_template_url_fr_${status_type}`];\r\n                delete params[`whatsapp_template_token_fr_${status_type}`];\r\n                delete params[`whatsapp_template_name_key_fr_${status_type}`];\r\n            }\r\n            delete params['status_type_fr_whatsapp_template'];\r\n            if (\r\n                subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' &&\r\n                values?.[`whatsapp_feedback_template_value`]\r\n            ) {\r\n                params['custom_whatsapp_feedback_templates'] =\r\n                    getWhatsAppFeedbackTemplate(values);\r\n                delete params[`whatsapp_feedback_template_value`];\r\n                delete params[`whatsapp_feedback_template_label`];\r\n                delete params[`whatsapp_feedback_template_name_key`];\r\n            }\r\n            if (\r\n                subTabKeyFrCustomSmsTemplate == 'consumer-otp-template' &&\r\n                values?.[`consumer_otp_template_fr_otp_sms_value`]\r\n            ) {\r\n                params['consumer_otp_sms_templates'] =\r\n                    getConsumerOTPTemplate(values);\r\n                delete params[`consumer_otp_template_fr_otp_sms_value`];\r\n                delete params[`consumer_otp_template_fr_otp_sms_label`];\r\n            }\r\n            if (\r\n                subTabKeyFrCustomSmsTemplate == 'sms-feedback-template' &&\r\n                values?.[`sms_feedback_template_value`]\r\n            ) {\r\n                params['custom_sms_feedback_templates'] =\r\n                    getSmsFeedbackTemplate(values);\r\n                delete params[`sms_feedback_template_value`];\r\n                delete params[`sms_feedback_template_label`];\r\n                delete params[`sms_feedback_template_name_key`];\r\n            }\r\n            console.log('params', params);\r\n            const onComplete = (resp) => {\r\n                resetCustomTemplateValues();\r\n                resetCustomWhatsAppTemplateValues();\r\n                resetWhatsAppFeedbackTemplateValues();\r\n                resetSmsFeedbackTemplateValues();\r\n                resetConsumerOTPTemplateValues();\r\n                setIsFormSubmitting(false);\r\n                message.success('Saved successfully');\r\n            };\r\n            const onError = (error) => {\r\n                setIsFormSubmitting(false);\r\n                message.error(http_utils.decodeErrorToMessage(error));\r\n            };\r\n            http_utils.performPostCall(submitUrl, params, onComplete, onError);\r\n        }\r\n    };\r\n\r\n    const prefillFormData = viewData?.form_data || {};\r\n    const resetCustomTemplateValues = () => {\r\n        let statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_sms_template'\r\n        );\r\n        const templateValueKey = `template_fr_${statusType}_value`;\r\n        const templateLabelKey = `template_fr_${statusType}_label`;\r\n        formRef.current.setFieldsValue({\r\n            status_type_fr_sms_template: undefined,\r\n            [templateValueKey]: undefined,\r\n            [templateLabelKey]: undefined,\r\n        });\r\n        setShowSmsTemplateCreationField(false);\r\n    };\r\n    const resetCustomWhatsAppTemplateValues = () => {\r\n        let statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_whatsapp_template'\r\n        );\r\n        const templateValueKey = `whatsapp_template_fr_${statusType}_value`;\r\n        const templateLabelKey = `whatsapp_template_fr_${statusType}_label`;\r\n        const templateUrlKey = `whatsapp_template_url_fr_${statusType}`;\r\n        const templateTokenKey = `whatsapp_template_token_fr_${statusType}`;\r\n        const templateNameKey = `whatsapp_template_name_key_fr_${statusType}`;\r\n        formRef.current.setFieldsValue({\r\n            status_type_fr_whatsapp_template: undefined,\r\n            [templateValueKey]: undefined,\r\n            [templateLabelKey]: undefined,\r\n            [templateUrlKey]: undefined,\r\n            [templateTokenKey]: undefined,\r\n            [templateNameKey]: undefined,\r\n        });\r\n        setShowWhatsAppTemplateCreationField(false);\r\n    };\r\n\r\n    const resetWhatsAppFeedbackTemplateValues = () => {\r\n        const templateValueKey = `whatsapp_feedback_template_value`;\r\n        const templateLabelKey = `whatsapp_feedback_template_label`;\r\n        const templateNameKey = `whatsapp_feedback_template_name_key`;\r\n        const templateTokenKey = `whatsapp_feedback_template_token`;\r\n        const templateUrlKey = `whatsapp_feedback_template_url`;\r\n        formRef.current.setFieldsValue({\r\n            [templateValueKey]: undefined,\r\n            [templateLabelKey]: undefined,\r\n            [templateNameKey]: undefined,\r\n            [templateTokenKey]: undefined,\r\n            [templateUrlKey]: undefined,\r\n        });\r\n    };\r\n    const resetSmsFeedbackTemplateValues = () => {\r\n        const templateValueKey = `sms_feedback_template_value`;\r\n        const templateLabelKey = `sms_feedback_template_label`;\r\n        const templateNameKey = `sms_feedback_template_name_key`;\r\n\r\n        formRef.current.setFieldsValue({\r\n            [templateValueKey]: undefined,\r\n            [templateLabelKey]: undefined,\r\n            [templateNameKey]: undefined,\r\n        });\r\n    };\r\n    const resetConsumerOTPTemplateValues = () => {\r\n        let statusType = 'otp_sms';\r\n        const templateValueKey = `consumer_otp_template_fr_${statusType}_value`;\r\n        const templateLabelKey = `consumer_otp_template_fr_${statusType}_label`;\r\n        formRef.current.setFieldsValue({\r\n            [templateValueKey]: undefined,\r\n            [templateLabelKey]: undefined,\r\n        });\r\n    };\r\n\r\n    const onStatusTypeChange = (statusType, currentStatusType) => {\r\n        if (showSmsTemplateCreationField) {\r\n            setShowSmsTemplateCreationField(false);\r\n            const templateValueKey = `template_fr_${statusType}_value`;\r\n            const templateLabelKey = `template_fr_${statusType}_label`;\r\n            formRef.current.setFieldsValue({\r\n                [templateValueKey]: undefined,\r\n                [templateLabelKey]: undefined,\r\n            });\r\n        }\r\n    };\r\n    const onWhatsAppTemplateStatusTypeChange = (\r\n        statusType,\r\n        currentStatusType\r\n    ) => {\r\n        if (showWhatsAppTemplateCreationField) {\r\n            setShowWhatsAppTemplateCreationField(false);\r\n            const templateValueKey = `whatsapp_template_fr_${statusType}_value`;\r\n            const templateLabelKey = `whatsapp_template_fr_${statusType}_label`;\r\n            const templateNameKey = `whatsapp_template_name_key_fr_${statusType}`;\r\n            formRef.current.setFieldsValue({\r\n                [templateValueKey]: undefined,\r\n                [templateLabelKey]: undefined,\r\n                [templateNameKey]: undefined,\r\n            });\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Extracts custom field metadata from provided template values and labels.\r\n     *\r\n     * @param {Object} params - The parameters object.\r\n     * @param {string} params.newCustomTemplateValue - The template string containing custom variables in `%CUSTOM_VAR_x%` format.\r\n     * @param {string} params.newCustomTemplateLabel - The template string containing custom labels in `<CUSTOM_VAR_x>` format.\r\n     * @returns {Array<{ key: string, label: string, required: boolean, rules: Array<{ max: number, message: string }> }>}\r\n     * An array of objects representing custom field metadata, each containing:\r\n     * - `key`: Extracted custom variable key.\r\n     * - `label`: Corresponding label from the template.\r\n     * - `required`: Always `true`.\r\n     * - `rules`: Validation rule limiting the maximum length to 30 characters.\r\n     */\r\n    const getCustomFieldsMeta = ({\r\n        newCustomTemplateValue,\r\n        newCustomTemplateLabel,\r\n    }) => {\r\n        let customFieldsMeta = [];\r\n        const customVariableFrValue = newCustomTemplateValue\r\n            ?.match(/%CUSTOM_VAR_([^%]+)%/g)\r\n            ?.map((m) => m.slice(1, -1));\r\n        const customVariableFrLabel = newCustomTemplateLabel\r\n            ?.match(/<CUSTOM_VAR_[^>]+>/g)\r\n            ?.map((m) => m.slice(1, -1));\r\n        if (customVariableFrValue) {\r\n            customVariableFrValue.forEach(\r\n                (singleCustomVariableFrValue, index) => {\r\n                    customFieldsMeta.push({\r\n                        key: singleCustomVariableFrValue,\r\n                        label: customVariableFrLabel?.[index],\r\n                        required: true,\r\n                        rules: [{ max: 30, message: 'max 30 character' }],\r\n                    });\r\n                }\r\n            );\r\n        }\r\n        return customFieldsMeta;\r\n    };\r\n\r\n    const getCustomSmsTemplate = (values) => {\r\n        let customSmsTemplates =\r\n            viewData?.form_data?.custom_sms_templates || [];\r\n        const statusType = values?.status_type_fr_sms_template;\r\n        const customTemplatePrefix = `template_fr_${statusType}`;\r\n        const newCustomTemplateValue =\r\n            values?.[`${customTemplatePrefix}_value`];\r\n        const newCustomTemplateLabel =\r\n            values?.[`${customTemplatePrefix}_label`];\r\n        const statusTypeCustomTemplateObject = customSmsTemplates?.find(\r\n            (singleCustomTemplate) => {\r\n                return singleCustomTemplate.value == statusType;\r\n            }\r\n        );\r\n        const customFieldsMeta = getCustomFieldsMeta({\r\n            newCustomTemplateValue,\r\n            newCustomTemplateLabel,\r\n        });\r\n        if (statusTypeCustomTemplateObject) {\r\n            customSmsTemplates.forEach((singleCustomSmsTemplate) => {\r\n                if (singleCustomSmsTemplate.value == statusType) {\r\n                    singleCustomSmsTemplate['templates'] = [\r\n                        ...singleCustomSmsTemplate['templates'],\r\n                        {\r\n                            value: newCustomTemplateValue,\r\n                            label: newCustomTemplateLabel,\r\n                            custom_fields_meta: customFieldsMeta,\r\n                        },\r\n                    ];\r\n                }\r\n            });\r\n        } else {\r\n            customSmsTemplates = [\r\n                ...customSmsTemplates,\r\n                {\r\n                    value: statusType,\r\n                    label: statusTypeCustomTemplateObject?.label,\r\n                    templates: [\r\n                        {\r\n                            value: newCustomTemplateValue,\r\n                            label: newCustomTemplateLabel,\r\n                            custom_fields_meta: customFieldsMeta,\r\n                        },\r\n                    ],\r\n                },\r\n            ];\r\n        }\r\n        return customSmsTemplates;\r\n    };\r\n    const getCustomWhatsAppTemplate = (values) => {\r\n        let customWhatsAppTemplates =\r\n            viewData?.form_data?.custom_whatsapp_templates || [];\r\n        const statusType = values?.status_type_fr_whatsapp_template;\r\n        const customTemplatePrefix = `whatsapp_template_fr_${statusType}`;\r\n        const customTemplateUrlfix = `whatsapp_template_url_fr_${statusType}`;\r\n        const customTemplateTokenfix = `whatsapp_template_token_fr_${statusType}`;\r\n        const newCustomTemplateNameKey =\r\n            values?.[`whatsapp_template_name_key_fr_${statusType}`];\r\n        const newCustomTemplateValue =\r\n            values?.[`${customTemplatePrefix}_value`];\r\n        const newCustomTemplateLabel =\r\n            values?.[`${customTemplatePrefix}_label`];\r\n        const newCustomTemplateUrl = values?.[customTemplateUrlfix];\r\n        const newCustomTemplateToken = values?.[customTemplateTokenfix];\r\n        const statusTypeCustomTemplateObject = customWhatsAppTemplates?.find(\r\n            (singleCustomTemplate) => {\r\n                return singleCustomTemplate.value == statusType;\r\n            }\r\n        );\r\n        const tempVariableFrValue = newCustomTemplateValue.match(/%([^%]+)%/g);\r\n        const customFieldsMeta = getCustomFieldsMeta({\r\n            newCustomTemplateValue,\r\n            newCustomTemplateLabel,\r\n        });\r\n        if (statusTypeCustomTemplateObject) {\r\n            customWhatsAppTemplates.forEach((singleCustomSmsTemplate) => {\r\n                if (singleCustomSmsTemplate.value == statusType) {\r\n                    singleCustomSmsTemplate['templates'] = [\r\n                        ...singleCustomSmsTemplate['templates'],\r\n                        {\r\n                            value: newCustomTemplateValue,\r\n                            label: newCustomTemplateLabel,\r\n                            variables_fr_api:\r\n                                getKeyValuePairFrWhatsAppTemplate(\r\n                                    tempVariableFrValue\r\n                                ),\r\n                            url: newCustomTemplateUrl,\r\n                            token: newCustomTemplateToken,\r\n                            template_name: newCustomTemplateNameKey,\r\n                            custom_fields_meta: customFieldsMeta,\r\n                        },\r\n                    ];\r\n                }\r\n            });\r\n        } else {\r\n            customWhatsAppTemplates = [\r\n                ...customWhatsAppTemplates,\r\n                {\r\n                    value: statusType,\r\n                    label: statusTypeCustomTemplateObject?.label,\r\n                    templates: [\r\n                        {\r\n                            value: newCustomTemplateValue,\r\n                            label: newCustomTemplateLabel,\r\n                            variables_fr_api:\r\n                                getKeyValuePairFrWhatsAppTemplate(\r\n                                    tempVariableFrValue\r\n                                ),\r\n                            url: newCustomTemplateUrl,\r\n                            token: newCustomTemplateToken,\r\n                            template_name: newCustomTemplateNameKey,\r\n                            custom_fields_meta: customFieldsMeta,\r\n                        },\r\n                    ],\r\n                },\r\n            ];\r\n        }\r\n        return customWhatsAppTemplates;\r\n    };\r\n\r\n    const getWhatsAppFeedbackTemplate = (values) => {\r\n        let CustomWhatsAppFeedbackTemplates =\r\n            viewData?.form_data?.custom_whatsapp_feedback_templates || [];\r\n        const customTemplatePrefix = `whatsapp_feedback_template`;\r\n        const newCustomTemplateValue =\r\n            values?.[`${customTemplatePrefix}_value`];\r\n        const newCustomTemplateLabel =\r\n            values?.[`${customTemplatePrefix}_label`];\r\n        const newCustomTemplateNameKey =\r\n            values?.[`${customTemplatePrefix}_name_key`];\r\n        const newCustomTemplateToken =\r\n            values?.[`${customTemplatePrefix}_token`];\r\n        const newCustomTemplateUrl = values?.[`${customTemplatePrefix}_url`];\r\n        const tempVariableFrValue = newCustomTemplateValue.match(/%([^%]+)%/g);\r\n        CustomWhatsAppFeedbackTemplates = [\r\n            ...CustomWhatsAppFeedbackTemplates,\r\n            {\r\n                value: newCustomTemplateValue,\r\n                label: newCustomTemplateLabel,\r\n                variables_fr_api:\r\n                    getKeyValuePairFrWhatsAppTemplate(tempVariableFrValue),\r\n                template_name: newCustomTemplateNameKey,\r\n                url: newCustomTemplateUrl,\r\n                token: newCustomTemplateToken,\r\n            },\r\n        ];\r\n\r\n        return CustomWhatsAppFeedbackTemplates;\r\n    };\r\n    const getSmsFeedbackTemplate = (values) => {\r\n        let CustomSmsFeedbackTemplates =\r\n            viewData?.form_data?.custom_sms_feedback_templates || [];\r\n        const customTemplatePrefix = `sms_feedback_template`;\r\n        const newCustomTemplateValue =\r\n            values?.[`${customTemplatePrefix}_value`];\r\n        const newCustomTemplateLabel =\r\n            values?.[`${customTemplatePrefix}_label`];\r\n        const newCustomTemplateNameKey =\r\n            values?.[`${customTemplatePrefix}_name_key`];\r\n        CustomSmsFeedbackTemplates = [\r\n            ...CustomSmsFeedbackTemplates,\r\n            {\r\n                value: newCustomTemplateValue,\r\n                label: newCustomTemplateLabel,\r\n                template_name: newCustomTemplateNameKey,\r\n            },\r\n        ];\r\n\r\n        return CustomSmsFeedbackTemplates;\r\n    };\r\n\r\n    const getConsumerOTPTemplate = (values) => {\r\n        let ConsumerOtpSmsTemplates =\r\n            viewData?.form_data?.consumer_otp_sms_templates || [];\r\n        const statusType = 'otp_sms';\r\n        const customTemplatePrefix = `consumer_otp_template_fr_${statusType}`;\r\n        const newCustomTemplateValue =\r\n            values?.[`${customTemplatePrefix}_value`];\r\n        const newCustomTemplateLabel =\r\n            values?.[`${customTemplatePrefix}_label`];\r\n        const statusTypeCustomTemplateObject = ConsumerOtpSmsTemplates?.find(\r\n            (singleCustomTemplate) => {\r\n                return singleCustomTemplate.value == statusType;\r\n            }\r\n        );\r\n        const tempVariableFrValue = newCustomTemplateValue.match(/%([^%]+)%/g);\r\n        if (statusTypeCustomTemplateObject) {\r\n            ConsumerOtpSmsTemplates.forEach((singleCustomSmsTemplate) => {\r\n                if (singleCustomSmsTemplate.value == statusType) {\r\n                    singleCustomSmsTemplate['templates'] = [\r\n                        ...singleCustomSmsTemplate['templates'],\r\n                        {\r\n                            value: newCustomTemplateValue,\r\n                            label: newCustomTemplateLabel,\r\n                        },\r\n                    ];\r\n                }\r\n            });\r\n        } else {\r\n            ConsumerOtpSmsTemplates = [\r\n                ...ConsumerOtpSmsTemplates,\r\n                {\r\n                    value: statusType,\r\n                    label: statusTypeCustomTemplateObject?.label,\r\n                    templates: [\r\n                        {\r\n                            value: newCustomTemplateValue,\r\n                            label: newCustomTemplateLabel,\r\n                        },\r\n                    ],\r\n                },\r\n            ];\r\n        }\r\n        return ConsumerOtpSmsTemplates;\r\n    };\r\n\r\n    const getKeyValuePairFrWhatsAppTemplate = (variables) => {\r\n        const keyValuePairFrWhatsAppTemplate = {};\r\n        if (variables?.length > 0) {\r\n            variables.forEach((singleVariable, index) => {\r\n                keyValuePairFrWhatsAppTemplate[`parameters[${index}]`] =\r\n                    singleVariable;\r\n            });\r\n        }\r\n        return keyValuePairFrWhatsAppTemplate;\r\n    };\r\n\r\n    const statusTypeFrSmsTemplateApplyClick = () => {\r\n        if (formRef?.current?.getFieldValue('status_type_fr_sms_template')) {\r\n            setShowSmsTemplateCreationField(true);\r\n        } else {\r\n            message.info('Pls select status type');\r\n        }\r\n    };\r\n    const statusTypeWhatsAppTemplateApplyClick = () => {\r\n        if (\r\n            formRef?.current?.getFieldValue('status_type_fr_whatsapp_template')\r\n        ) {\r\n            setShowWhatsAppTemplateCreationField(true);\r\n        } else {\r\n            message.info('Pls select status type');\r\n        }\r\n    };\r\n\r\n    const getStatusTypeFrCustomTemplateCreation = () => {\r\n        const statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_sms_template'\r\n        );\r\n        const defaultValueVariablesFrStatuses = {\r\n            schedule_assignment: [\r\n                '%ext_order_id%',\r\n                '%subtask_slot%',\r\n                '%assignee_number%',\r\n                '%display_code%',\r\n            ],\r\n            re_scheduled: ['%ext_order_id%'],\r\n            tx_re_assignment: ['%ext_order_id%'],\r\n        };\r\n        const defaultLabelVariablesFrStatuses = {\r\n            schedule_assignment: [\r\n                '<External ID>',\r\n                '<Subtask Start and End time>',\r\n                '<Assignee Number>',\r\n                '<Request ID>',\r\n            ],\r\n            re_scheduled: ['<External ID>'],\r\n            tx_re_assignment: ['<External ID>'],\r\n        };\r\n        const status_type_fr_custom_template_creation_data =\r\n            status_type_templates_.map((singleStatusTypeTemplate) => {\r\n                let availableVariablesFrValue =\r\n                    defaultValueVariablesFrStatuses[statusType] || [];\r\n                let availableVariablesFrLabel =\r\n                    defaultLabelVariablesFrStatuses[statusType] || [];\r\n\r\n                singleStatusTypeTemplate.templates.forEach(\r\n                    (singleStatusTypeWiseTemplate) => {\r\n                        const templateValueString =\r\n                            singleStatusTypeWiseTemplate.value;\r\n                        const templateLabelString =\r\n                            singleStatusTypeWiseTemplate.label;\r\n\r\n                        const tempVariableFrValue =\r\n                            templateValueString.match(/%([^%]+)%/g);\r\n                        const tempVariableFrLabel =\r\n                            templateLabelString.match(/<([^>]+)>/g);\r\n\r\n                        if (tempVariableFrValue) {\r\n                            const words = tempVariableFrValue.filter(\r\n                                (match) => !match.includes('CUSTOM_VAR')\r\n                            );\r\n                            availableVariablesFrValue = [\r\n                                ...new Set([\r\n                                    ...availableVariablesFrValue,\r\n                                    ...words,\r\n                                ]),\r\n                            ];\r\n                        }\r\n                        if (tempVariableFrLabel) {\r\n                            const words = tempVariableFrLabel.filter(\r\n                                (match) => !match.includes('custom_var')\r\n                            );\r\n                            availableVariablesFrLabel = [\r\n                                ...new Set([\r\n                                    ...availableVariablesFrLabel,\r\n                                    ...words,\r\n                                ]),\r\n                            ];\r\n                        }\r\n                    }\r\n                );\r\n\r\n                return {\r\n                    value: singleStatusTypeTemplate.value,\r\n                    label: singleStatusTypeTemplate.label,\r\n                    available_variables: availableVariablesFrValue,\r\n                    available_label_variables: availableVariablesFrLabel,\r\n                };\r\n            });\r\n\r\n        return status_type_fr_custom_template_creation_data;\r\n    };\r\n    const getStatusTypeFrCustomTemplateCreationFrWhatsApp = () => {\r\n        const statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_whatsapp_template'\r\n        );\r\n        const defaultValueVariablesFrStatuses = {\r\n            schedule_assignment: [\r\n                '%ext_order_id%',\r\n                '%assignee_number%',\r\n                '%display_code%',\r\n            ],\r\n            re_scheduled: ['%ext_order_id%'],\r\n            tx_re_assignment: ['%ext_order_id%'],\r\n            request_closure: ['%link%'],\r\n        };\r\n        const defaultLabelVariablesFrStatuses = {\r\n            schedule_assignment: [\r\n                '<External ID>',\r\n                '<Assignee Number>',\r\n                '<Request ID>',\r\n            ],\r\n            re_scheduled: ['<External ID>'],\r\n            tx_re_assignment: ['<External ID>'],\r\n            request_closure: ['<link>'],\r\n        };\r\n        const status_type_fr_custom_template_creation_data =\r\n            status_type_templates_fr_whatsapp.map(\r\n                (singleStatusTypeTemplate) => {\r\n                    let availableVariablesFrValue =\r\n                        defaultValueVariablesFrStatuses[statusType] || [];\r\n                    let availableVariablesFrLabel =\r\n                        defaultLabelVariablesFrStatuses[statusType] || [];\r\n\r\n                    singleStatusTypeTemplate.templates.forEach(\r\n                        (singleStatusTypeWiseTemplate) => {\r\n                            const templateValueString =\r\n                                singleStatusTypeWiseTemplate.value;\r\n                            const templateLabelString =\r\n                                singleStatusTypeWiseTemplate.label;\r\n\r\n                            const tempVariableFrValue =\r\n                                templateValueString.match(/%([^%]+)%/g);\r\n                            const tempVariableFrLabel =\r\n                                templateLabelString.match(/<([^>]+)>/g);\r\n\r\n                            if (tempVariableFrValue) {\r\n                                const words = tempVariableFrValue.filter(\r\n                                    (match) => !match.includes('CUSTOM_VAR')\r\n                                );\r\n                                availableVariablesFrValue = [\r\n                                    ...new Set([\r\n                                        ...availableVariablesFrValue,\r\n                                        ...words,\r\n                                    ]),\r\n                                ];\r\n                            }\r\n                            if (tempVariableFrLabel) {\r\n                                const words = tempVariableFrLabel.filter(\r\n                                    (match) => !match.includes('custom_var')\r\n                                );\r\n                                availableVariablesFrLabel = [\r\n                                    ...new Set([\r\n                                        ...availableVariablesFrLabel,\r\n                                        ...words,\r\n                                    ]),\r\n                                ];\r\n                            }\r\n                        }\r\n                    );\r\n\r\n                    return {\r\n                        value: singleStatusTypeTemplate.value,\r\n                        label: singleStatusTypeTemplate.label,\r\n                        available_variables: availableVariablesFrValue,\r\n                        available_label_variables: availableVariablesFrLabel,\r\n                    };\r\n                }\r\n            );\r\n\r\n        return status_type_fr_custom_template_creation_data;\r\n    };\r\n    const getWhatsAppFeedbackTemplateCreation = () => {\r\n        const custom_Whatsapp_feedback_template_creation_data =\r\n            consumer_feedback_templates_fr_whatsapp.map(\r\n                (singleStatusTypeTemplate) => {\r\n                    let availableVariablesFrValue = [];\r\n                    let availableVariablesFrLabel = [];\r\n\r\n                    const templateValueString = singleStatusTypeTemplate.value;\r\n                    const templateLabelString = singleStatusTypeTemplate.label;\r\n\r\n                    const tempVariableFrValue =\r\n                        templateValueString.match(/%([^%]+)%/g);\r\n                    const tempVariableFrLabel =\r\n                        templateLabelString.match(/<([^>]+)>/g);\r\n\r\n                    if (tempVariableFrValue) {\r\n                        const words = tempVariableFrValue.filter(\r\n                            (match) => !match.includes('CUSTOM_VAR')\r\n                        );\r\n                        availableVariablesFrValue = [\r\n                            ...new Set([\r\n                                ...availableVariablesFrValue,\r\n                                ...words,\r\n                            ]),\r\n                        ];\r\n                    }\r\n                    if (tempVariableFrLabel) {\r\n                        const words = tempVariableFrLabel.filter(\r\n                            (match) => !match.includes('custom_var')\r\n                        );\r\n                        availableVariablesFrLabel = [\r\n                            ...new Set([\r\n                                ...availableVariablesFrLabel,\r\n                                ...words,\r\n                            ]),\r\n                        ];\r\n                    }\r\n\r\n                    return {\r\n                        available_variables: availableVariablesFrValue,\r\n                        available_label_variables: availableVariablesFrLabel,\r\n                    };\r\n                }\r\n            );\r\n\r\n        return custom_Whatsapp_feedback_template_creation_data;\r\n    };\r\n    const getStatusTypeFrConsumerOTPTemplateCreation = () => {\r\n        const custom_sbtsk_sms_template_creation_data =\r\n            sbtsk_sms_templates_.map((singleStatusTypeTemplate) => {\r\n                let availableVariablesFrValue = [];\r\n                let availableVariablesFrLabel = [];\r\n\r\n                singleStatusTypeTemplate.templates.forEach(\r\n                    (singleStatusTypeWiseTemplate) => {\r\n                        const templateValueString =\r\n                            singleStatusTypeWiseTemplate.value;\r\n                        const templateLabelString =\r\n                            singleStatusTypeWiseTemplate.label;\r\n\r\n                        const tempVariableFrValue =\r\n                            templateValueString.match(/%([^%]+)%/g);\r\n                        const tempVariableFrLabel =\r\n                            templateLabelString.match(/<([^>]+)>/g);\r\n\r\n                        if (tempVariableFrValue) {\r\n                            const words = tempVariableFrValue.filter(\r\n                                (match) => !match.includes('CUSTOM_VAR')\r\n                            );\r\n                            availableVariablesFrValue = [\r\n                                ...new Set([\r\n                                    ...availableVariablesFrValue,\r\n                                    ...words,\r\n                                ]),\r\n                            ];\r\n                        }\r\n                        if (tempVariableFrLabel) {\r\n                            const words = tempVariableFrLabel.filter(\r\n                                (match) => !match.includes('custom_var')\r\n                            );\r\n                            availableVariablesFrLabel = [\r\n                                ...new Set([\r\n                                    ...availableVariablesFrLabel,\r\n                                    ...words,\r\n                                ]),\r\n                            ];\r\n                        }\r\n                    }\r\n                );\r\n\r\n                return {\r\n                    value: singleStatusTypeTemplate.value,\r\n                    label: singleStatusTypeTemplate.label,\r\n                    available_variables: availableVariablesFrValue,\r\n                    available_label_variables: availableVariablesFrLabel,\r\n                };\r\n            });\r\n\r\n        return custom_sbtsk_sms_template_creation_data;\r\n    };\r\n\r\n    const validateCustomTemplateValueOrLabel = (rule, value, callback) => {\r\n        const params = rule.params;\r\n        const checkFrValue = rule.checkFrValue;\r\n        const checkFrLabel = rule.checkFrLabel;\r\n        let validFlag = true;\r\n        let tempVariableFrValueOrLabel;\r\n        if (checkFrValue) {\r\n            tempVariableFrValueOrLabel = value?.match(/%([^%]+)%/g);\r\n            if (\r\n                (subTabKeyFrCommunications == 'sms-template' &&\r\n                    subTabKeyFrCustomSmsTemplate == undefined) ||\r\n                subTabKeyFrCustomSmsTemplate == 'add-sms-template' ||\r\n                (subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                    subTabKeyFrCustomWhatsAppTemplate == undefined) ||\r\n                subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template'\r\n            ) {\r\n                tempVariableFrValueOrLabel = tempVariableFrValueOrLabel?.filter(\r\n                    (singleValue) => !/^%CUSTOM_VAR_\\d+%$/.test(singleValue) // Exclude %CUSTOM_VAR_1%, %CUSTOM_VAR_2%, etc.\r\n                );\r\n            }\r\n        }\r\n        if (checkFrLabel) {\r\n            if (\r\n                (subTabKeyFrCommunications == 'sms-template' &&\r\n                    subTabKeyFrCustomSmsTemplate == undefined) ||\r\n                subTabKeyFrCustomSmsTemplate == 'add-sms-template' ||\r\n                (subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                    subTabKeyFrCustomWhatsAppTemplate == undefined) ||\r\n                subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template'\r\n            ) {\r\n                tempVariableFrValueOrLabel = value?.match(\r\n                    /<(?!CUSTOM_VAR_)([^>]+)>/g\r\n                );\r\n            } else {\r\n                tempVariableFrValueOrLabel = value?.match(/<([^>]+)>/g);\r\n            }\r\n        }\r\n        // Check if any element in the array is present in the string\r\n        if (tempVariableFrValueOrLabel) {\r\n            validFlag = !tempVariableFrValueOrLabel.some(\r\n                (element) => !params.includes(element)\r\n            );\r\n        }\r\n        if (validFlag) {\r\n            callback(); // Validation passed\r\n        } else {\r\n            callback('Cannot save template, invalid variable values or labels'); // Validation failed\r\n        }\r\n    };\r\n\r\n    const getSmsTemplateCreationMeta = () => {\r\n        let status_type_fr_custom_template_creation =\r\n            getStatusTypeFrCustomTemplateCreation();\r\n        const statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_sms_template'\r\n        );\r\n        const selected_status_type_value_and_label =\r\n            status_type_fr_custom_template_creation?.find(\r\n                (singleStatusTypeCustomTemplate) => {\r\n                    return (\r\n                        singleStatusTypeCustomTemplate.value ==\r\n                        formRef?.current?.getFieldValue(\r\n                            'status_type_fr_sms_template'\r\n                        )\r\n                    );\r\n                }\r\n            );\r\n\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                // {\r\n                //   key : 'template_creation_heading',\r\n                //   render : () => {\r\n                //     return (\r\n                //       <div className=\"gx-mb-3\">\r\n                //         <b>Add SMS template</b>\r\n                //       </div>\r\n                //     )\r\n                //   }\r\n                // },\r\n                ...(subTabKeyFrCommunications == 'sms-template'\r\n                    ? [\r\n                          {\r\n                              key: `status_type_fr_sms_template`,\r\n                              label: 'Status Type',\r\n                              widget: 'select',\r\n                              placeholder: 'Select status type',\r\n                              onChange: (value) => {\r\n                                  onStatusTypeChange(statusType, value);\r\n                                  forceUpdate();\r\n                              },\r\n                              options: status_type_fr_custom_template_creation,\r\n                              widgetProps: {\r\n                                  allowClear: true,\r\n                                  showSearch: true,\r\n                                  optionFilterProp: 'children',\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'apply',\r\n                    render: () => {\r\n                        return (\r\n                            <Button\r\n                                onClick={statusTypeFrSmsTemplateApplyClick}\r\n                                type=\"primary\"\r\n                            >\r\n                                apply\r\n                            </Button>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                (subTabKeyFrCustomSmsTemplate == undefined ||\r\n                    subTabKeyFrCustomSmsTemplate == 'add-sms-template') &&\r\n                showSmsTemplateCreationField &&\r\n                formRef?.current?.getFieldValue('status_type_fr_sms_template')\r\n                    ? [\r\n                          {\r\n                              key: 'value_varaibles',\r\n                              render: () => {\r\n                                  return (\r\n                                      <div>\r\n                                          Use below variables, to create new\r\n                                          template\r\n                                          <br></br>\r\n                                          {selected_status_type_value_and_label?.available_variables.map(\r\n                                              (reminder, index) => (\r\n                                                  <Tag\r\n                                                      key={index}\r\n                                                      color=\"orange\"\r\n                                                      className=\"highlighted-tag\"\r\n                                                  >\r\n                                                      {reminder}\r\n                                                  </Tag>\r\n                                              )\r\n                                          )}\r\n                                      </div>\r\n                                  );\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                (subTabKeyFrCustomSmsTemplate == undefined ||\r\n                    subTabKeyFrCustomSmsTemplate == 'add-sms-template') &&\r\n                showSmsTemplateCreationField &&\r\n                formRef?.current?.getFieldValue('status_type_fr_sms_template')\r\n                    ? [\r\n                          {\r\n                              key: `template_fr_${selected_status_type_value_and_label?.value}_value`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template value for{' '}\r\n                                      <b>\r\n                                          {\r\n                                              selected_status_type_value_and_label?.label\r\n                                          }\r\n                                      </b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: selected_status_type_value_and_label?.available_variables,\r\n                                      checkFrValue: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                (subTabKeyFrCustomSmsTemplate == undefined ||\r\n                    subTabKeyFrCustomSmsTemplate == 'add-sms-template') &&\r\n                showSmsTemplateCreationField &&\r\n                formRef?.current?.getFieldValue('status_type_fr_sms_template')\r\n                    ? [\r\n                          {\r\n                              key: 'label_varaibles',\r\n                              render: () => {\r\n                                  return (\r\n                                      <div>\r\n                                          Use below variables, to create new\r\n                                          template\r\n                                          <br></br>\r\n                                          {selected_status_type_value_and_label?.available_label_variables.map(\r\n                                              (reminder, index) => (\r\n                                                  <Tag\r\n                                                      key={index}\r\n                                                      color=\"orange\"\r\n                                                      className=\"highlighted-tag\"\r\n                                                  >\r\n                                                      {reminder}\r\n                                                  </Tag>\r\n                                              )\r\n                                          )}\r\n                                      </div>\r\n                                  );\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                (subTabKeyFrCustomSmsTemplate == undefined ||\r\n                    subTabKeyFrCustomSmsTemplate == 'add-sms-template') &&\r\n                showSmsTemplateCreationField &&\r\n                formRef?.current?.getFieldValue('status_type_fr_sms_template')\r\n                    ? [\r\n                          {\r\n                              key: `template_fr_${selected_status_type_value_and_label?.value}_label`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template label for{' '}\r\n                                      <b>\r\n                                          {\r\n                                              selected_status_type_value_and_label?.label\r\n                                          }\r\n                                      </b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: selected_status_type_value_and_label?.available_label_variables,\r\n                                      checkFrLabel: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n    const getWhatsAppTemplateCreationMeta = () => {\r\n        let status_type_fr_custom_template_creation =\r\n            getStatusTypeFrCustomTemplateCreationFrWhatsApp();\r\n        const statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_whatsapp_template'\r\n        );\r\n        const selected_status_type_value_and_label =\r\n            status_type_fr_custom_template_creation?.find(\r\n                (singleStatusTypeCustomTemplate) => {\r\n                    return (\r\n                        singleStatusTypeCustomTemplate.value ==\r\n                        formRef?.current?.getFieldValue(\r\n                            'status_type_fr_whatsapp_template'\r\n                        )\r\n                    );\r\n                }\r\n            );\r\n\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template'\r\n                    ? [\r\n                          {\r\n                              key: `status_type_fr_whatsapp_template`,\r\n                              label: 'Status Type',\r\n                              widget: 'select',\r\n                              placeholder: 'Select status type',\r\n                              onChange: (value) => {\r\n                                  onWhatsAppTemplateStatusTypeChange(\r\n                                      statusType,\r\n                                      value\r\n                                  );\r\n                                  forceUpdate();\r\n                              },\r\n                              options: status_type_fr_custom_template_creation,\r\n                              widgetProps: {\r\n                                  allowClear: true,\r\n                                  showSearch: true,\r\n                                  optionFilterProp: 'children',\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'apply',\r\n                    render: () => {\r\n                        return (\r\n                            <Button\r\n                                onClick={statusTypeWhatsAppTemplateApplyClick}\r\n                                type=\"primary\"\r\n                            >\r\n                                apply\r\n                            </Button>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n                    subTabKeyFrCustomWhatsAppTemplate ==\r\n                        'add-whatsapp-template') &&\r\n                showWhatsAppTemplateCreationField &&\r\n                formRef?.current?.getFieldValue(\r\n                    'status_type_fr_whatsapp_template'\r\n                )\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_template_name_key_fr_${selected_status_type_value_and_label?.value}`,\r\n                              label: (\r\n                                  <span>\r\n                                      Template Name for{' '}\r\n                                      <b>\r\n                                          {\r\n                                              selected_status_type_value_and_label?.label\r\n                                          }\r\n                                      </b>\r\n                                  </span>\r\n                              ),\r\n                              onChange: (value) => forceUpdate(),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n                    subTabKeyFrCustomWhatsAppTemplate ==\r\n                        'add-whatsapp-template') &&\r\n                showWhatsAppTemplateCreationField &&\r\n                formRef?.current?.getFieldValue(\r\n                    'status_type_fr_whatsapp_template'\r\n                )\r\n                    ? [\r\n                          {\r\n                              key: 'whatsapp_value_varaibles',\r\n                              render: () => {\r\n                                  return (\r\n                                      <div>\r\n                                          Use below variables, to create new\r\n                                          template\r\n                                          <br></br>\r\n                                          {selected_status_type_value_and_label?.available_variables.map(\r\n                                              (reminder, index) => (\r\n                                                  <Tag\r\n                                                      key={index}\r\n                                                      color=\"orange\"\r\n                                                      className=\"highlighted-tag\"\r\n                                                  >\r\n                                                      {reminder}\r\n                                                  </Tag>\r\n                                              )\r\n                                          )}\r\n                                      </div>\r\n                                  );\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n                    subTabKeyFrCustomWhatsAppTemplate ==\r\n                        'add-whatsapp-template') &&\r\n                showWhatsAppTemplateCreationField &&\r\n                formRef?.current?.getFieldValue(\r\n                    'status_type_fr_whatsapp_template'\r\n                )\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_template_fr_${selected_status_type_value_and_label?.value}_value`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template value for{' '}\r\n                                      <b>\r\n                                          {\r\n                                              selected_status_type_value_and_label?.label\r\n                                          }\r\n                                      </b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: selected_status_type_value_and_label?.available_variables,\r\n                                      checkFrValue: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n                    subTabKeyFrCustomWhatsAppTemplate ==\r\n                        'add-whatsapp-template') &&\r\n                showWhatsAppTemplateCreationField &&\r\n                formRef?.current?.getFieldValue(\r\n                    'status_type_fr_whatsapp_template'\r\n                )\r\n                    ? [\r\n                          {\r\n                              key: 'whatsapp_label_varaibles',\r\n                              render: () => {\r\n                                  return (\r\n                                      <div>\r\n                                          Use below variables, to create new\r\n                                          template\r\n                                          <br></br>\r\n                                          {selected_status_type_value_and_label?.available_label_variables.map(\r\n                                              (reminder, index) => (\r\n                                                  <Tag\r\n                                                      key={index}\r\n                                                      color=\"orange\"\r\n                                                      className=\"highlighted-tag\"\r\n                                                  >\r\n                                                      {reminder}\r\n                                                  </Tag>\r\n                                              )\r\n                                          )}\r\n                                      </div>\r\n                                  );\r\n                              },\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n                    subTabKeyFrCustomWhatsAppTemplate ==\r\n                        'add-whatsapp-template') &&\r\n                showWhatsAppTemplateCreationField &&\r\n                formRef?.current?.getFieldValue(\r\n                    'status_type_fr_whatsapp_template'\r\n                )\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_template_fr_${selected_status_type_value_and_label?.value}_label`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template label for{' '}\r\n                                      <b>\r\n                                          {\r\n                                              selected_status_type_value_and_label?.label\r\n                                          }\r\n                                      </b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: selected_status_type_value_and_label?.available_label_variables,\r\n                                      checkFrLabel: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n                    subTabKeyFrCustomWhatsAppTemplate ==\r\n                        'add-whatsapp-template') &&\r\n                showWhatsAppTemplateCreationField &&\r\n                formRef?.current?.getFieldValue(\r\n                    'status_type_fr_whatsapp_template'\r\n                )\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_template_url_fr_${selected_status_type_value_and_label?.value}`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add url for new template{' '}\r\n                                      <b>\r\n                                          {\r\n                                              selected_status_type_value_and_label?.label\r\n                                          }\r\n                                      </b>\r\n                                  </span>\r\n                              ),\r\n                              // formItemProps : {\r\n                              //   style : {\r\n                              //     display: 'none'\r\n                              //   }\r\n                              // },\r\n                              // onChange: (value) => forceUpdate(),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n                    subTabKeyFrCustomWhatsAppTemplate ==\r\n                        'add-whatsapp-template') &&\r\n                showWhatsAppTemplateCreationField &&\r\n                formRef?.current?.getFieldValue(\r\n                    'status_type_fr_whatsapp_template'\r\n                )\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_template_token_fr_${selected_status_type_value_and_label?.value}`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add token for new template{' '}\r\n                                      <b>\r\n                                          {\r\n                                              selected_status_type_value_and_label?.label\r\n                                          }\r\n                                      </b>\r\n                                  </span>\r\n                              ),\r\n                              // formItemProps : {\r\n                              //   style : {\r\n                              //     display: 'none'\r\n                              //   }\r\n                              // },\r\n                              // onChange: (value) => forceUpdate(),\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    const getSmsFeedbackMessageTemplateCreationMeta = () => {\r\n        const defaultValueVariables = [\r\n            '%cust_name%',\r\n            '%display_code%',\r\n            '%feedback_link%',\r\n        ];\r\n        const defaultLabelVariables = [\r\n            '<customer name>',\r\n            '<Req ID>',\r\n            '<feedback_link>',\r\n        ];\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                subTabKeyFrCustomSmsTemplate == 'sms-feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `sms_feedback_template_name_key`,\r\n                              label: <span>Template Name</span>,\r\n                              onChange: (value) => forceUpdate(),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'sms_feedback_value_varaibles',\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                Use below variables, to create new template\r\n                                <br></br>\r\n                                {defaultValueVariables.map(\r\n                                    (reminder, index) => (\r\n                                        <Tag\r\n                                            key={index}\r\n                                            color=\"orange\"\r\n                                            className=\"highlighted-tag\"\r\n                                        >\r\n                                            {reminder}\r\n                                        </Tag>\r\n                                    )\r\n                                )}\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                subTabKeyFrCustomSmsTemplate == 'sms-feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `sms_feedback_template_value`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template value for{' '}\r\n                                      <b>Consumer Feedback</b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: defaultValueVariables,\r\n                                      checkFrValue: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'sms_feedback_label_varaibles',\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                Use below variables, to create new template\r\n                                <br></br>\r\n                                {defaultLabelVariables.map(\r\n                                    (reminder, index) => (\r\n                                        <Tag\r\n                                            key={index}\r\n                                            color=\"orange\"\r\n                                            className=\"highlighted-tag\"\r\n                                        >\r\n                                            {reminder}\r\n                                        </Tag>\r\n                                    )\r\n                                )}\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                subTabKeyFrCustomSmsTemplate == 'sms-feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `sms_feedback_template_label`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template label for{' '}\r\n                                      <b>Consumer Feedback</b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: defaultLabelVariables,\r\n                                      checkFrLabel: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    const getWhatsAppFeedbackMessageTemplateCreationMeta = () => {\r\n        let status_type_fr_custom_template_creation =\r\n            getWhatsAppFeedbackTemplateCreation();\r\n        const whatsapp_feedback_template_value_and_label =\r\n            status_type_fr_custom_template_creation[0];\r\n\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                subTabKeyFrCustomWhatsAppTemplate == 'feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_feedback_template_name_key`,\r\n                              label: <span>Template Name</span>,\r\n                              onChange: (value) => forceUpdate(),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'whatsapp_feedback_value_varaibles',\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                Use below variables, to create new template\r\n                                <br></br>\r\n                                {whatsapp_feedback_template_value_and_label?.available_variables.map(\r\n                                    (reminder, index) => (\r\n                                        <Tag\r\n                                            key={index}\r\n                                            color=\"orange\"\r\n                                            className=\"highlighted-tag\"\r\n                                        >\r\n                                            {reminder}\r\n                                        </Tag>\r\n                                    )\r\n                                )}\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                subTabKeyFrCustomWhatsAppTemplate == 'feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_feedback_template_value`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template value for{' '}\r\n                                      <b>Consumer Feedback</b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: whatsapp_feedback_template_value_and_label?.available_variables,\r\n                                      checkFrValue: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'whatsapp_feedback_label_varaibles',\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                Use below variables, to create new template\r\n                                <br></br>\r\n                                {whatsapp_feedback_template_value_and_label?.available_label_variables.map(\r\n                                    (reminder, index) => (\r\n                                        <Tag\r\n                                            key={index}\r\n                                            color=\"orange\"\r\n                                            className=\"highlighted-tag\"\r\n                                        >\r\n                                            {reminder}\r\n                                        </Tag>\r\n                                    )\r\n                                )}\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                subTabKeyFrCustomWhatsAppTemplate == 'feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_feedback_template_label`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template label for{' '}\r\n                                      <b>Consumer Feedback</b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: whatsapp_feedback_template_value_and_label?.available_label_variables,\r\n                                      checkFrLabel: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                subTabKeyFrCustomWhatsAppTemplate == 'feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_feedback_template_token`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template token for{' '}\r\n                                      <b>Consumer Feedback</b>\r\n                                  </span>\r\n                              ),\r\n                              onChange: (value) => forceUpdate(),\r\n                          },\r\n                      ]\r\n                    : []),\r\n                ...(subTabKeyFrCommunications == 'whatsapp-template' &&\r\n                subTabKeyFrCustomWhatsAppTemplate == 'feedback-template'\r\n                    ? [\r\n                          {\r\n                              key: `whatsapp_feedback_template_url`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template url for{' '}\r\n                                      <b>Consumer Feedback</b>\r\n                                  </span>\r\n                              ),\r\n                              onChange: (value) => forceUpdate(),\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    const getConsumerOtpTemplateCreationMeta = () => {\r\n        let status_type_fr_custom_template_creation =\r\n            getStatusTypeFrConsumerOTPTemplateCreation();\r\n        const selected_otp_sms_value_and_label =\r\n            status_type_fr_custom_template_creation?.find(\r\n                (singleStatusTypeCustomTemplate) => {\r\n                    return singleStatusTypeCustomTemplate.value == 'otp_sms';\r\n                }\r\n            );\r\n\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                {\r\n                    key: 'consumer_otp_value_varaibles',\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                Use below variables, to create new template\r\n                                <br></br>\r\n                                {selected_otp_sms_value_and_label?.available_variables.map(\r\n                                    (reminder, index) => (\r\n                                        <Tag\r\n                                            key={index}\r\n                                            color=\"orange\"\r\n                                            className=\"highlighted-tag\"\r\n                                        >\r\n                                            {reminder}\r\n                                        </Tag>\r\n                                    )\r\n                                )}\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                subTabKeyFrCustomSmsTemplate == 'consumer-otp-template'\r\n                    ? [\r\n                          {\r\n                              key: `consumer_otp_template_fr_${selected_otp_sms_value_and_label?.value}_value`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template value for{' '}\r\n                                      <b>Consumer OTP</b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: selected_otp_sms_value_and_label?.available_variables,\r\n                                      checkFrValue: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n                {\r\n                    key: 'consumer_otp_label_varaibles',\r\n                    render: () => {\r\n                        return (\r\n                            <div>\r\n                                Use below variables, to create new template\r\n                                <br></br>\r\n                                {selected_otp_sms_value_and_label?.available_label_variables.map(\r\n                                    (reminder, index) => (\r\n                                        <Tag\r\n                                            key={index}\r\n                                            color=\"orange\"\r\n                                            className=\"highlighted-tag\"\r\n                                        >\r\n                                            {reminder}\r\n                                        </Tag>\r\n                                    )\r\n                                )}\r\n                            </div>\r\n                        );\r\n                    },\r\n                },\r\n                ...(subTabKeyFrCommunications == 'sms-template' &&\r\n                subTabKeyFrCustomSmsTemplate == 'consumer-otp-template'\r\n                    ? [\r\n                          {\r\n                              key: `consumer_otp_template_fr_${selected_otp_sms_value_and_label?.value}_label`,\r\n                              label: (\r\n                                  <span>\r\n                                      Add new template label for{' '}\r\n                                      <b>Consumer OTP</b>\r\n                                  </span>\r\n                              ),\r\n                              required: true,\r\n                              onChange: (value) => forceUpdate(),\r\n                              rules: [\r\n                                  {\r\n                                      validator:\r\n                                          validateCustomTemplateValueOrLabel,\r\n                                      params: selected_otp_sms_value_and_label?.available_label_variables,\r\n                                      checkFrLabel: true,\r\n                                      message:\r\n                                          'Cannot save template, invalid variable values or labels',\r\n                                  },\r\n                              ],\r\n                          },\r\n                      ]\r\n                    : []),\r\n            ],\r\n        };\r\n        return meta;\r\n    };\r\n\r\n    const setCurrentSubTabFrCommunications = (tabNmae) => {\r\n        setSubTabKeyFrCommunications(tabNmae);\r\n    };\r\n\r\n    const setCurrentSubTabFrCustomWhatsAppTemplate = (tabNmae) => {\r\n        setSubTabKeyFrCustomWhatsAppTemplate(tabNmae);\r\n    };\r\n\r\n    const setCurrentSubTabFrCustomSmsTemplate = (tabNmae) => {\r\n        setSubTabKeyFrCustomSmsTemplate(tabNmae);\r\n    };\r\n\r\n    const setCurrentTabNmae = (tabNmae) => {\r\n        setTabName(tabNmae);\r\n    };\r\n\r\n    const readyToSubmitSmsTemplateCreation = () => {\r\n        let statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_sms_template'\r\n        );\r\n        const templateValueKey = `template_fr_${statusType}_value`;\r\n        const templateLabelKey = `template_fr_${statusType}_label`;\r\n        const templateValue = formRef?.current?.getFieldValue(templateValueKey);\r\n        const templateLabel = formRef?.current?.getFieldValue(templateLabelKey);\r\n        return (subTabKeyFrCustomSmsTemplate == undefined ||\r\n            subTabKeyFrCustomSmsTemplate == 'add-sms-template') &&\r\n            subTabKeyFrCommunications == 'sms-template' &&\r\n            templateValue &&\r\n            templateLabel\r\n            ? true\r\n            : false;\r\n    };\r\n    const readyToSubmitWhatsAppsTemplateCreation = () => {\r\n        let statusType = formRef?.current?.getFieldValue(\r\n            'status_type_fr_whatsapp_template'\r\n        );\r\n        const templateValueKey = `whatsapp_template_fr_${statusType}_value`;\r\n        const templateLabelKey = `whatsapp_template_fr_${statusType}_label`;\r\n        // const templateNameKey = `whatsapp_template_name_key_fr_${statusType}`;\r\n        const templateValue = formRef?.current?.getFieldValue(templateValueKey);\r\n        const templateLabel = formRef?.current?.getFieldValue(templateLabelKey);\r\n        // const templateName = formRef?.current?.getFieldValue(templateNameKey)\r\n        return (subTabKeyFrCustomWhatsAppTemplate == undefined ||\r\n            subTabKeyFrCustomWhatsAppTemplate == 'add-whatsapp-template') &&\r\n            subTabKeyFrCommunications != 'sms-template' &&\r\n            templateValue &&\r\n            templateLabel\r\n            ? true\r\n            : false;\r\n    };\r\n\r\n    const readyToSubmitWhatsAppFeedbackTemplateCreation = () => {\r\n        const templateValueKey = `whatsapp_feedback_template_value`;\r\n        const templateLabelKey = `whatsapp_feedback_template_label`;\r\n        // const templateNameKey = `whatsapp_feedback_template_name_key`;\r\n        const templateValue = formRef?.current?.getFieldValue(templateValueKey);\r\n        const templateLabel = formRef?.current?.getFieldValue(templateLabelKey);\r\n        // const templateName = formRef?.current?.getFieldValue(templateNameKey);\r\n        return subTabKeyFrCustomWhatsAppTemplate == 'feedback-template' &&\r\n            subTabKeyFrCommunications == 'whatsapp-template' &&\r\n            templateValue &&\r\n            templateLabel\r\n            ? true\r\n            : false;\r\n    };\r\n    const readyToSubmitSmsFeedbackTemplateCreation = () => {\r\n        const templateValueKey = `sms_feedback_template_value`;\r\n        const templateLabelKey = `sms_feedback_template_label`;\r\n        // const templateNameKey = `whatsapp_feedback_template_name_key`;\r\n        const templateValue = formRef?.current?.getFieldValue(templateValueKey);\r\n        const templateLabel = formRef?.current?.getFieldValue(templateLabelKey);\r\n        // const templateName = formRef?.current?.getFieldValue(templateNameKey);\r\n        console.log('templateValue', templateValue);\r\n        console.log('templateLabel', templateLabel);\r\n        console.log(\r\n            'subTabKeyFrCustomSmsTemplate',\r\n            subTabKeyFrCustomSmsTemplate\r\n        );\r\n        console.log('subTabKeyFrCommunications', subTabKeyFrCommunications);\r\n        return subTabKeyFrCustomSmsTemplate == 'sms-feedback-template' &&\r\n            subTabKeyFrCommunications == 'sms-template' &&\r\n            templateValue &&\r\n            templateLabel\r\n            ? true\r\n            : false;\r\n    };\r\n\r\n    const readyToSubmitConsumerOTPTemplateCreation = () => {\r\n        let statusType = 'otp_sms';\r\n        const templateValueKey = `consumer_otp_template_fr_${statusType}_value`;\r\n        const templateLabelKey = `consumer_otp_template_fr_${statusType}_label`;\r\n        const templateValue = formRef?.current?.getFieldValue(templateValueKey);\r\n        const templateLabel = formRef?.current?.getFieldValue(templateLabelKey);\r\n        return subTabKeyFrCustomSmsTemplate == 'consumer-otp-template' &&\r\n            subTabKeyFrCommunications == 'sms-template' &&\r\n            templateValue &&\r\n            templateLabel\r\n            ? true\r\n            : false;\r\n    };\r\n    console.log(\r\n        'readyToSubmitSmsFeedbackTemplateCreation',\r\n        readyToSubmitSmsFeedbackTemplateCreation()\r\n    );\r\n    return (\r\n        <div>\r\n            {isLoadingViewData ? (\r\n                <div className=\"gx-loader-view gx-loader-position\">\r\n                    <CircularProgress />\r\n                </div>\r\n            ) : viewData == undefined ? (\r\n                <p className=\"gx-text-red\">{error}</p>\r\n            ) : (\r\n                <Widget>\r\n                    <>\r\n                        <Form\r\n                            className=\"gx-mt-3\"\r\n                            layout=\"vertical\"\r\n                            ref={formRef}\r\n                            onFinish={handleFinish}\r\n                            initialValues={prefillFormData}\r\n                        >\r\n                            <Tabs\r\n                                defaultActiveKey=\"regional\"\r\n                                onChange={(activeKey) =>\r\n                                    setCurrentTabNmae(activeKey)\r\n                                }\r\n                            >\r\n                                <Tabs.TabPane\r\n                                    tab={<span>Regional</span>}\r\n                                    key=\"regional\"\r\n                                    forceRender={true}\r\n                                >\r\n                                    <Alert\r\n                                        message=\"This setting is used to select the timezone that will be considered for subtask creation, rescheduling for this organization.\"\r\n                                        description=\"Note: In case no selection is done, Indian Standard Time is considered by default.\"\r\n                                        type=\"info\"\r\n                                        showIcon\r\n                                    />\r\n                                    <FormBuilder\r\n                                        meta={getRegionalMeta()}\r\n                                        form={formRef}\r\n                                    />\r\n                                </Tabs.TabPane>\r\n\r\n                                <Tabs.TabPane\r\n                                    tab={<span>Communications</span>}\r\n                                    key=\"communication\"\r\n                                    forceRender={true}\r\n                                >\r\n                                    <Tabs\r\n                                        defaultActiveKey=\"lambda\"\r\n                                        onChange={(activeKey) =>\r\n                                            setCurrentSubTabFrCommunications(\r\n                                                activeKey\r\n                                            )\r\n                                        }\r\n                                    >\r\n                                        <Tabs.TabPane\r\n                                            tab=\"Lambda\"\r\n                                            key=\"lambda\"\r\n                                            forceRender={true}\r\n                                        >\r\n                                            <Alert\r\n                                                // className=\"gx-my-1 gx-mt-2\"\r\n                                                message=\"About communications...\"\r\n                                                description=\"If you don't know what lambda means, leave the menu right away! This section helps you configure custom phone numbers/apis/providers that will be used for communication via WhatsApp, SMS and Voice calling\"\r\n                                                type=\"info\"\r\n                                                showIcon\r\n                                            />\r\n                                            <FormBuilder\r\n                                                meta={getCommunicationMeta()}\r\n                                                form={formRef}\r\n                                            />\r\n                                        </Tabs.TabPane>\r\n                                        <Tabs.TabPane\r\n                                            tab=\"SMS Template\"\r\n                                            key=\"sms-template\"\r\n                                            forceRender={true}\r\n                                        >\r\n                                            <Tabs\r\n                                                defaultActiveKey=\"add-sms-template\"\r\n                                                onChange={(activeKey) =>\r\n                                                    setCurrentSubTabFrCustomSmsTemplate(\r\n                                                        activeKey\r\n                                                    )\r\n                                                }\r\n                                            >\r\n                                                <Tabs.TabPane\r\n                                                    tab=\"Add SMS Template\"\r\n                                                    key=\"add-sms-template\"\r\n                                                    forceRender={true}\r\n                                                >\r\n                                                    {/* Create a new component for Billing to be done later */}\r\n                                                    <FormBuilder\r\n                                                        form={formRef}\r\n                                                        meta={getSmsTemplateCreationMeta()}\r\n                                                    />\r\n                                                </Tabs.TabPane>\r\n                                                <Tabs.TabPane\r\n                                                    tab=\"Consumer OTP Template\"\r\n                                                    key=\"consumer-otp-template\"\r\n                                                    forceRender={true}\r\n                                                >\r\n                                                    {/* Create a new component for Billing to be done later */}\r\n                                                    <FormBuilder\r\n                                                        form={formRef}\r\n                                                        meta={getConsumerOtpTemplateCreationMeta()}\r\n                                                    />\r\n                                                </Tabs.TabPane>\r\n                                                <Tabs.TabPane\r\n                                                    tab=\"SMS Feedback Template\"\r\n                                                    key=\"sms-feedback-template\"\r\n                                                    forceRender={true}\r\n                                                >\r\n                                                    {/* Create a new component for Billing to be done later */}\r\n                                                    <FormBuilder\r\n                                                        form={formRef}\r\n                                                        meta={getSmsFeedbackMessageTemplateCreationMeta()}\r\n                                                    />\r\n                                                </Tabs.TabPane>\r\n                                            </Tabs>\r\n                                        </Tabs.TabPane>\r\n                                        <Tabs.TabPane\r\n                                            tab=\"WhatsApp Template\"\r\n                                            key=\"whatsapp-template\"\r\n                                            forceRender={true}\r\n                                        >\r\n                                            <Tabs\r\n                                                defaultActiveKey=\"add-whatsapp-template\"\r\n                                                onChange={(activeKey) =>\r\n                                                    setCurrentSubTabFrCustomWhatsAppTemplate(\r\n                                                        activeKey\r\n                                                    )\r\n                                                }\r\n                                            >\r\n                                                <Tabs.TabPane\r\n                                                    tab=\"Status Wise Template\"\r\n                                                    key=\"add-whatsapp-template\"\r\n                                                    forceRender={true}\r\n                                                >\r\n                                                    {/* Create a new component for Billing to be done later */}\r\n                                                    <FormBuilder\r\n                                                        form={formRef}\r\n                                                        meta={getWhatsAppTemplateCreationMeta()}\r\n                                                    />\r\n                                                </Tabs.TabPane>\r\n                                                <Tabs.TabPane\r\n                                                    tab=\"Feedback Template\"\r\n                                                    key=\"feedback-template\"\r\n                                                    forceRender={true}\r\n                                                >\r\n                                                    {/* Create a new component for Billing to be done later */}\r\n                                                    <FormBuilder\r\n                                                        form={formRef}\r\n                                                        meta={getWhatsAppFeedbackMessageTemplateCreationMeta()}\r\n                                                    />\r\n                                                </Tabs.TabPane>\r\n                                            </Tabs>\r\n                                        </Tabs.TabPane>\r\n                                    </Tabs>\r\n                                </Tabs.TabPane>\r\n\r\n                                {ConfigHelpers.isBrand() && (\r\n                                    <Tabs.TabPane\r\n                                        tab={<span>Customer Settings</span>}\r\n                                        key=\"customer-settings\"\r\n                                        forceRender={true}\r\n                                    >\r\n                                        <CustomerSettings formRef={formRef} />\r\n                                    </Tabs.TabPane>\r\n                                )}\r\n                            </Tabs>\r\n                            {(tabName != 'communication' ||\r\n                                tabName == 'customer-settings' ||\r\n                                subTabKeyFrCommunications == undefined ||\r\n                                subTabKeyFrCommunications == 'lambda' ||\r\n                                (subTabKeyFrCustomWhatsAppTemplate !=\r\n                                    undefined &&\r\n                                    subTabKeyFrCustomWhatsAppTemplate !=\r\n                                        'add-whatsapp-template' &&\r\n                                    subTabKeyFrCustomWhatsAppTemplate !=\r\n                                        'feedback-template' &&\r\n                                    subTabKeyFrCommunications !=\r\n                                        'sms-template' &&\r\n                                    subTabKeyFrCustomSmsTemplate != undefined &&\r\n                                    subTabKeyFrCustomSmsTemplate !=\r\n                                        'add-sms-template' &&\r\n                                    subTabKeyFrCustomSmsTemplate !=\r\n                                        'consumer-otp-template' &&\r\n                                    subTabKeyFrCustomSmsTemplate !=\r\n                                        'sms-feedback-template' &&\r\n                                    subTabKeyFrCommunications !=\r\n                                        'whatsapp-template') ||\r\n                                readyToSubmitSmsTemplateCreation() ||\r\n                                readyToSubmitWhatsAppsTemplateCreation() ||\r\n                                readyToSubmitWhatsAppFeedbackTemplateCreation() ||\r\n                                readyToSubmitSmsFeedbackTemplateCreation() ||\r\n                                readyToSubmitConsumerOTPTemplateCreation()) && (\r\n                                <Form.Item className=\"form-footer\">\r\n                                    {isFormSubmitting && <Spin />}\r\n                                    <Button\r\n                                        htmlType=\"submit\"\r\n                                        type=\"primary\"\r\n                                        className=\"gx-mb-0\"\r\n                                        disabled={isFormSubmitting}\r\n                                    >\r\n                                        Submit\r\n                                    </Button>\r\n                                </Form.Item>\r\n                            )}\r\n                        </Form>\r\n                    </>\r\n                </Widget>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Settings;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AACpE,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,0BAA0B;AAC/E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,sBAAsB,QAAQ,6DAA6D;AACpG,SAASC,iCAAiC,QAAQ,kEAAkE;AACpH,SAASC,oBAAoB,QAAQ,+DAA+D;AACpG,SAASC,uCAAuC,QAAQ,sDAAsD;AAC9G,OAAOC,gBAAgB,MAAM,oBAAoB;AAEjD,MAAMC,QAAQ,GAAG,uBAAuB;AACxC,MAAMC,SAAS,GAAG,iBAAiB;AAEnC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACnB,MAAMC,OAAO,GAAGxB,MAAM,CAAC,CAAC;EACxB,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC4B,SAAS,CAAC;EACnD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMmC,WAAW,GAAG3B,WAAW,CAAC4B,cAAc,CAAC,CAAC;EAChD,MAAM,CAACC,4BAA4B,EAAEC,+BAA+B,CAAC,GACjEtC,QAAQ,CAAC,KAAK,CAAC;EACnB,MAAM,CACFuC,iCAAiC,EACjCC,oCAAoC,CACvC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnB,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC4B,SAAS,CAAC;EACjD,MAAM,CAACe,yBAAyB,EAAEC,4BAA4B,CAAC,GAC3D5C,QAAQ,CAAC4B,SAAS,CAAC;EACvB,MAAM,CACFiB,iCAAiC,EACjCC,oCAAoC,CACvC,GAAG9C,QAAQ,CAAC4B,SAAS,CAAC;EACvB,MAAM,CAACmB,4BAA4B,EAAEC,+BAA+B,CAAC,GACjEhD,QAAQ,CAAC4B,SAAS,CAAC;EAEvB9B,SAAS,CAAC,MAAM;IACZmD,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAI,CAAChB,iBAAiB,EAAE;MACpBC,oBAAoB,CAAC,IAAI,CAAC;MAC1BP,WAAW,CAACC,SAAS,CAAC;MACtBE,QAAQ,CAACF,SAAS,CAAC;MACnB,IAAIsB,MAAM,GAAG,CAAC,CAAC;MACf,MAAMC,UAAU,GAAIC,IAAI,IAAK;QACzBlB,oBAAoB,CAAC,KAAK,CAAC;QAC3BP,WAAW,CAACyB,IAAI,CAACC,IAAI,CAAC;QACtBC,UAAU,CAAC,MAAM;UACbnB,WAAW,CAAC,CAAC;QACjB,CAAC,EAAE,GAAG,CAAC;MACX,CAAC;MACD,MAAMoB,OAAO,GAAI1B,KAAK,IAAK;QACvBK,oBAAoB,CAAC,KAAK,CAAC;QAC3BJ,QAAQ,CAACnB,UAAU,CAAC6C,oBAAoB,CAAC3B,KAAK,CAAC,CAAC;MACpD,CAAC;MACDlB,UAAU,CAAC8C,cAAc,CAACrC,QAAQ,EAAE8B,MAAM,EAAEC,UAAU,EAAEI,OAAO,CAAC;IACpE;EACJ,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,4BAA4B;QACjCC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,2CAA2C;QAClDC,OAAO,EACH,4GAA4G;QAChHC,WAAW,EAAE;UACTC,UAAU,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE,CACH;UACIC,SAAS,EAAEzD,iBAAiB;UAC5BN,OAAO,EAAE;QACb,CAAC;MAET,CAAC,EACD;QACIwD,GAAG,EAAE,kCAAkC;QACvCC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,8CAA8C;QACrDC,OAAO,EACH,2IAA2I;QAC/IC,WAAW,EAAE;UACTC,UAAU,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE,CACH;UACIC,SAAS,EAAEzD,iBAAiB;UAC5BN,OAAO,EAAE;QACb,CAAC;MAET,CAAC,EACD;QACIwD,GAAG,EAAE,6BAA6B;QAClCC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,wCAAwC;QAC/CC,OAAO,EACH,qIAAqI;QACzIC,WAAW,EAAE;UACTC,UAAU,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE,CACH;UACIC,SAAS,EAAEzD,iBAAiB;UAC5BN,OAAO,EAAE;QACb,CAAC;MAET,CAAC;IAET,CAAC;IACD,OAAOoD,IAAI;EACf,CAAC;EAED,MAAMY,yBAAyB,GAAGA,CAAA,KAAM;IACpC,IAAIlB,IAAI,GAAG,EAAE;IACb3B,QAAQ,CAAC8C,SAAS,CAACC,GAAG,CAAEC,cAAc,IAAK;MACvCrB,IAAI,CAACsB,IAAI,CAAC;QACNC,KAAK,EAAEF,cAAc,CAACG,QAAQ;QAC9BZ,KAAK,EAAE,GAAGS,cAAc,CAACI,OAAO,MAAMJ,cAAc,CAACG,QAAQ,YAAYH,cAAc,CAACK,UAAU;MACtG,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO1B,IAAI;EACf,CAAC;EAED,MAAM2B,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI3B,IAAI,GAAG,EAAE;IACb3B,QAAQ,CAACuD,6BAA6B,CAACR,GAAG,CAAES,aAAa,IAAK;MAC1D7B,IAAI,CAACsB,IAAI,CAAC;QACNC,KAAK,EAAEM,aAAa,CAACC,UAAU;QAC/BlB,KAAK,EAAE,GAAGiB,aAAa,CAACJ,OAAO,KAAKI,aAAa,CAACE,YAAY,MAAMF,aAAa,CAACC,UAAU;MAChG,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO9B,IAAI;EACf,CAAC;EAED,MAAMgC,4BAA4B,GAAGA,CAAA,KAAM;IACvC,IAAIhC,IAAI,GAAG,EAAE;IACb3B,QAAQ,CAAC4D,QAAQ,CAACb,GAAG,CAAEc,iBAAiB,IAAK;MACzClC,IAAI,CAACsB,IAAI,CAAC;QACNC,KAAK,EAAEW,iBAAiB,CAACH,YAAY;QACrCnB,KAAK,EAAE,GAAGsB,iBAAiB,CAACT,OAAO,MAAMS,iBAAiB,CAACH,YAAY;MAC3E,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO/B,IAAI;EACf,CAAC;EAED,MAAMmC,gBAAgB,GAAGA,CAACC,WAAW,EAAEC,cAAc,KAAK;IACtD,MAAMC,YAAY,GAAGD,cAAc,CAACE,IAAI,CACnCd,OAAO,IAAKA,OAAO,CAACM,YAAY,KAAKK,WAC1C,CAAC;IACD,OAAOE,YAAY,GAAGA,YAAY,CAACE,cAAc,GAAG,IAAI;EAC5D,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B,OAAO;MACHlC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,qBAAqB;QAC1BE,KAAK,EAAE,8BAA8B;QACrCD,MAAM,EAAE,QAAQ;QAChB+B,OAAO,EAAExB,yBAAyB,CAAC,CAAC;QACpCJ,WAAW,EAAE;UACT;UACA6B,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE,UAAU;UAC5BC,QAAQ,EAAGtB,KAAK,IAAK;YACjBhE,iBAAiB,CACbgE,KAAK,EACLrD,OAAO,EACP,qBACJ,CAAC;UACL;QACJ;MACJ,CAAC,EACD;QACIwC,GAAG,EAAE,2CAA2C;QAChDE,KAAK,EAAE,2CAA2C;QAClDD,MAAM,EAAE,QAAQ;QAChB+B,OAAO,EAAEf,oBAAoB,CAAC,CAAC;QAC/Bb,WAAW,EAAE;UACT;UACA6B,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE;QACtB;MACJ,CAAC,EACD;QACIlC,GAAG,EAAE,8CAA8C;QACnDE,KAAK,EAAE,8CAA8C;QACrDD,MAAM,EAAE,QAAQ;QAChBE,OAAO,EACH,+FAA+F;QACnGiC,QAAQ,EAAE,IAAI;QACdhC,WAAW,EAAE;UACT;UACA6B,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE,UAAU;UAC5B5B,KAAK,EAAE,CACH;YACI+B,IAAI,EAAE,QAAQ;YACdC,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE;UACT,CAAC;QAET;MACJ,CAAC,EACD;QACIvC,GAAG,EAAE,qBAAqB;QAC1BE,KAAK,EAAE,8CAA8C;QACrDD,MAAM,EAAE,QAAQ;QAChB+B,OAAO,EAAEV,4BAA4B,CAAC,CAAC;QACvClB,WAAW,EAAE;UACT;UACA6B,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE,UAAU;UAC5BC,QAAQ,EAAGtB,KAAK,IAAK;YACjB,IAAIrD,OAAO,EAAE;cACTA,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;gBAC3BC,+BAA+B,EAC3BjB,gBAAgB,CACZZ,KAAK,EACLlD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4D,QACd;cACR,CAAC,CAAC;YACN;YACA1E,iBAAiB,CACbgE,KAAK,EACLrD,OAAO,EACP,qBACJ,CAAC;UACL;QACJ;MACJ,CAAC,EACD;QACIwC,GAAG,EAAE,iCAAiC;QACtCE,KAAK,EAAE,6BAA6B;QACpCD,MAAM,EAAE,QAAQ;QAChB0C,QAAQ,EAAE,IAAI;QACdvC,WAAW,EAAE;UACT;UACA8B,gBAAgB,EAAE,UAAU;UAC5BC,QAAQ,EAAGtB,KAAK,IAAK;YACjBhE,iBAAiB,CACbgE,KAAK,EACLrD,OAAO,EACP,iCACJ,CAAC;UACL;QACJ;MACJ,CAAC;IAET,CAAC;EACL,CAAC;EAGD,MAAMoF,YAAY,GAAIC,MAAM,IAAK;IAC7B,IAAI,CAAC7E,gBAAgB,EAAE;MACnBC,mBAAmB,CAAC,IAAI,CAAC;MACzB,IAAIkB,MAAM,GAAG0D,MAAM;MACnB,IACIjE,yBAAyB,IAAI,cAAc,KAC3CiE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,2BAA2B,MACnCD,MAAM,aAANA,MAAM,uBAANA,MAAM,CACF,eAAeA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,2BAA2B,QAAQ,CAC7D,GACH;QACE,MAAMC,WAAW,GAAGF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,2BAA2B;QACvD3D,MAAM,CAAC,sBAAsB,CAAC,GAAG6D,oBAAoB,CAACH,MAAM,CAAC;QAC7D,OAAO1D,MAAM,CAAC,eAAe4D,WAAW,QAAQ,CAAC;QACjD,OAAO5D,MAAM,CAAC,eAAe4D,WAAW,QAAQ,CAAC;MACrD;MACA,OAAO5D,MAAM,CAAC,6BAA6B,CAAC;MAC5C,IACIP,yBAAyB,IAAI,mBAAmB,KAChDiE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,gCAAgC,MACxCJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CACF,wBAAwBA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,gCAAgC,QAAQ,CAC3E,GACH;QACE,MAAMF,WAAW,GAAGF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,gCAAgC;QAC5D9D,MAAM,CAAC,2BAA2B,CAAC,GAC/B+D,yBAAyB,CAACL,MAAM,CAAC;QACrC,OAAO1D,MAAM,CAAC,wBAAwB4D,WAAW,QAAQ,CAAC;QAC1D,OAAO5D,MAAM,CAAC,wBAAwB4D,WAAW,QAAQ,CAAC;QAC1D,OAAO5D,MAAM,CAAC,4BAA4B4D,WAAW,EAAE,CAAC;QACxD,OAAO5D,MAAM,CAAC,8BAA8B4D,WAAW,EAAE,CAAC;QAC1D,OAAO5D,MAAM,CAAC,iCAAiC4D,WAAW,EAAE,CAAC;MACjE;MACA,OAAO5D,MAAM,CAAC,kCAAkC,CAAC;MACjD,IACIL,iCAAiC,IAAI,mBAAmB,KACxD+D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,kCAAkC,CAAC,GAC9C;QACE1D,MAAM,CAAC,oCAAoC,CAAC,GACxCgE,2BAA2B,CAACN,MAAM,CAAC;QACvC,OAAO1D,MAAM,CAAC,kCAAkC,CAAC;QACjD,OAAOA,MAAM,CAAC,kCAAkC,CAAC;QACjD,OAAOA,MAAM,CAAC,qCAAqC,CAAC;MACxD;MACA,IACIH,4BAA4B,IAAI,uBAAuB,KACvD6D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,wCAAwC,CAAC,GACpD;QACE1D,MAAM,CAAC,4BAA4B,CAAC,GAChCiE,sBAAsB,CAACP,MAAM,CAAC;QAClC,OAAO1D,MAAM,CAAC,wCAAwC,CAAC;QACvD,OAAOA,MAAM,CAAC,wCAAwC,CAAC;MAC3D;MACA,IACIH,4BAA4B,IAAI,uBAAuB,KACvD6D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,6BAA6B,CAAC,GACzC;QACE1D,MAAM,CAAC,+BAA+B,CAAC,GACnCkE,sBAAsB,CAACR,MAAM,CAAC;QAClC,OAAO1D,MAAM,CAAC,6BAA6B,CAAC;QAC5C,OAAOA,MAAM,CAAC,6BAA6B,CAAC;QAC5C,OAAOA,MAAM,CAAC,gCAAgC,CAAC;MACnD;MACAmE,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEpE,MAAM,CAAC;MAC7B,MAAMC,UAAU,GAAIC,IAAI,IAAK;QACzBmE,yBAAyB,CAAC,CAAC;QAC3BC,iCAAiC,CAAC,CAAC;QACnCC,mCAAmC,CAAC,CAAC;QACrCC,8BAA8B,CAAC,CAAC;QAChCC,8BAA8B,CAAC,CAAC;QAChC3F,mBAAmB,CAAC,KAAK,CAAC;QAC1BzB,OAAO,CAACqH,OAAO,CAAC,oBAAoB,CAAC;MACzC,CAAC;MACD,MAAMrE,OAAO,GAAI1B,KAAK,IAAK;QACvBG,mBAAmB,CAAC,KAAK,CAAC;QAC1BzB,OAAO,CAACsB,KAAK,CAAClB,UAAU,CAAC6C,oBAAoB,CAAC3B,KAAK,CAAC,CAAC;MACzD,CAAC;MACDlB,UAAU,CAACkH,eAAe,CAACxG,SAAS,EAAE6B,MAAM,EAAEC,UAAU,EAAEI,OAAO,CAAC;IACtE;EACJ,CAAC;EAED,MAAMuE,eAAe,GAAG,CAAApG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqG,SAAS,KAAI,CAAC,CAAC;EACjD,MAAMR,yBAAyB,GAAGA,CAAA,KAAM;IAAA,IAAAS,gBAAA;IACpC,IAAIC,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAAyG,gBAAA,GAAPzG,OAAO,CAAEgF,OAAO,cAAAyB,gBAAA,uBAAhBA,gBAAA,CAAkBE,aAAa,CAC5C,6BACJ,CAAC;IACD,MAAMC,gBAAgB,GAAG,eAAeF,UAAU,QAAQ;IAC1D,MAAMG,gBAAgB,GAAG,eAAeH,UAAU,QAAQ;IAC1D1G,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;MAC3BK,2BAA2B,EAAEjF,SAAS;MACtC,CAACuG,gBAAgB,GAAGvG,SAAS;MAC7B,CAACwG,gBAAgB,GAAGxG;IACxB,CAAC,CAAC;IACFU,+BAA+B,CAAC,KAAK,CAAC;EAC1C,CAAC;EACD,MAAMkF,iCAAiC,GAAGA,CAAA,KAAM;IAAA,IAAAa,iBAAA;IAC5C,IAAIJ,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAA8G,iBAAA,GAAP9G,OAAO,CAAEgF,OAAO,cAAA8B,iBAAA,uBAAhBA,iBAAA,CAAkBH,aAAa,CAC5C,kCACJ,CAAC;IACD,MAAMC,gBAAgB,GAAG,wBAAwBF,UAAU,QAAQ;IACnE,MAAMG,gBAAgB,GAAG,wBAAwBH,UAAU,QAAQ;IACnE,MAAMK,cAAc,GAAG,4BAA4BL,UAAU,EAAE;IAC/D,MAAMM,gBAAgB,GAAG,8BAA8BN,UAAU,EAAE;IACnE,MAAMO,eAAe,GAAG,iCAAiCP,UAAU,EAAE;IACrE1G,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;MAC3BQ,gCAAgC,EAAEpF,SAAS;MAC3C,CAACuG,gBAAgB,GAAGvG,SAAS;MAC7B,CAACwG,gBAAgB,GAAGxG,SAAS;MAC7B,CAAC0G,cAAc,GAAG1G,SAAS;MAC3B,CAAC2G,gBAAgB,GAAG3G,SAAS;MAC7B,CAAC4G,eAAe,GAAG5G;IACvB,CAAC,CAAC;IACFY,oCAAoC,CAAC,KAAK,CAAC;EAC/C,CAAC;EAED,MAAMiF,mCAAmC,GAAGA,CAAA,KAAM;IAC9C,MAAMU,gBAAgB,GAAG,kCAAkC;IAC3D,MAAMC,gBAAgB,GAAG,kCAAkC;IAC3D,MAAMI,eAAe,GAAG,qCAAqC;IAC7D,MAAMD,gBAAgB,GAAG,kCAAkC;IAC3D,MAAMD,cAAc,GAAG,gCAAgC;IACvD/G,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;MAC3B,CAAC2B,gBAAgB,GAAGvG,SAAS;MAC7B,CAACwG,gBAAgB,GAAGxG,SAAS;MAC7B,CAAC4G,eAAe,GAAG5G,SAAS;MAC5B,CAAC2G,gBAAgB,GAAG3G,SAAS;MAC7B,CAAC0G,cAAc,GAAG1G;IACtB,CAAC,CAAC;EACN,CAAC;EACD,MAAM8F,8BAA8B,GAAGA,CAAA,KAAM;IACzC,MAAMS,gBAAgB,GAAG,6BAA6B;IACtD,MAAMC,gBAAgB,GAAG,6BAA6B;IACtD,MAAMI,eAAe,GAAG,gCAAgC;IAExDjH,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;MAC3B,CAAC2B,gBAAgB,GAAGvG,SAAS;MAC7B,CAACwG,gBAAgB,GAAGxG,SAAS;MAC7B,CAAC4G,eAAe,GAAG5G;IACvB,CAAC,CAAC;EACN,CAAC;EACD,MAAM+F,8BAA8B,GAAGA,CAAA,KAAM;IACzC,IAAIM,UAAU,GAAG,SAAS;IAC1B,MAAME,gBAAgB,GAAG,4BAA4BF,UAAU,QAAQ;IACvE,MAAMG,gBAAgB,GAAG,4BAA4BH,UAAU,QAAQ;IACvE1G,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;MAC3B,CAAC2B,gBAAgB,GAAGvG,SAAS;MAC7B,CAACwG,gBAAgB,GAAGxG;IACxB,CAAC,CAAC;EACN,CAAC;EAED,MAAM6G,kBAAkB,GAAGA,CAACR,UAAU,EAAES,iBAAiB,KAAK;IAC1D,IAAIrG,4BAA4B,EAAE;MAC9BC,+BAA+B,CAAC,KAAK,CAAC;MACtC,MAAM6F,gBAAgB,GAAG,eAAeF,UAAU,QAAQ;MAC1D,MAAMG,gBAAgB,GAAG,eAAeH,UAAU,QAAQ;MAC1D1G,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;QAC3B,CAAC2B,gBAAgB,GAAGvG,SAAS;QAC7B,CAACwG,gBAAgB,GAAGxG;MACxB,CAAC,CAAC;IACN;EACJ,CAAC;EACD,MAAM+G,kCAAkC,GAAGA,CACvCV,UAAU,EACVS,iBAAiB,KAChB;IACD,IAAInG,iCAAiC,EAAE;MACnCC,oCAAoC,CAAC,KAAK,CAAC;MAC3C,MAAM2F,gBAAgB,GAAG,wBAAwBF,UAAU,QAAQ;MACnE,MAAMG,gBAAgB,GAAG,wBAAwBH,UAAU,QAAQ;MACnE,MAAMO,eAAe,GAAG,iCAAiCP,UAAU,EAAE;MACrE1G,OAAO,CAACgF,OAAO,CAACC,cAAc,CAAC;QAC3B,CAAC2B,gBAAgB,GAAGvG,SAAS;QAC7B,CAACwG,gBAAgB,GAAGxG,SAAS;QAC7B,CAAC4G,eAAe,GAAG5G;MACvB,CAAC,CAAC;IACN;EACJ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMgH,mBAAmB,GAAGA,CAAC;IACzBC,sBAAsB;IACtBC;EACJ,CAAC,KAAK;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IACF,IAAIC,gBAAgB,GAAG,EAAE;IACzB,MAAMC,qBAAqB,GAAGL,sBAAsB,aAAtBA,sBAAsB,wBAAAE,qBAAA,GAAtBF,sBAAsB,CAC9CM,KAAK,CAAC,uBAAuB,CAAC,cAAAJ,qBAAA,uBADNA,qBAAA,CAExBtE,GAAG,CAAE2E,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC,MAAMC,qBAAqB,GAAGR,sBAAsB,aAAtBA,sBAAsB,wBAAAE,qBAAA,GAAtBF,sBAAsB,CAC9CK,KAAK,CAAC,qBAAqB,CAAC,cAAAH,qBAAA,uBADJA,qBAAA,CAExBvE,GAAG,CAAE2E,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC,IAAIH,qBAAqB,EAAE;MACvBA,qBAAqB,CAACK,OAAO,CACzB,CAACC,2BAA2B,EAAEC,KAAK,KAAK;QACpCR,gBAAgB,CAACtE,IAAI,CAAC;UAClBZ,GAAG,EAAEyF,2BAA2B;UAChCvF,KAAK,EAAEqF,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAGG,KAAK,CAAC;UACrCtD,QAAQ,EAAE,IAAI;UACd9B,KAAK,EAAE,CAAC;YAAEiC,GAAG,EAAE,EAAE;YAAE/F,OAAO,EAAE;UAAmB,CAAC;QACpD,CAAC,CAAC;MACN,CACJ,CAAC;IACL;IACA,OAAO0I,gBAAgB;EAC3B,CAAC;EAED,MAAMlC,oBAAoB,GAAIH,MAAM,IAAK;IAAA,IAAA8C,mBAAA,EAAAC,mBAAA;IACrC,IAAIC,kBAAkB,GAClB,CAAAlI,QAAQ,aAARA,QAAQ,wBAAAgI,mBAAA,GAARhI,QAAQ,CAAEqG,SAAS,cAAA2B,mBAAA,uBAAnBA,mBAAA,CAAqBG,oBAAoB,KAAI,EAAE;IACnD,MAAM5B,UAAU,GAAGrB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,2BAA2B;IACtD,MAAMiD,oBAAoB,GAAG,eAAe7B,UAAU,EAAE;IACxD,MAAMY,sBAAsB,GACxBjC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMhB,sBAAsB,GACxBlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMC,8BAA8B,IAAAJ,mBAAA,GAAGC,kBAAkB,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoB/D,IAAI,CAC1DoE,oBAAoB,IAAK;MACtB,OAAOA,oBAAoB,CAACpF,KAAK,IAAIqD,UAAU;IACnD,CACJ,CAAC;IACD,MAAMgB,gBAAgB,GAAGL,mBAAmB,CAAC;MACzCC,sBAAsB;MACtBC;IACJ,CAAC,CAAC;IACF,IAAIiB,8BAA8B,EAAE;MAChCH,kBAAkB,CAACL,OAAO,CAAEU,uBAAuB,IAAK;QACpD,IAAIA,uBAAuB,CAACrF,KAAK,IAAIqD,UAAU,EAAE;UAC7CgC,uBAAuB,CAAC,WAAW,CAAC,GAAG,CACnC,GAAGA,uBAAuB,CAAC,WAAW,CAAC,EACvC;YACIrF,KAAK,EAAEiE,sBAAsB;YAC7B5E,KAAK,EAAE6E,sBAAsB;YAC7BoB,kBAAkB,EAAEjB;UACxB,CAAC,CACJ;QACL;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACHW,kBAAkB,GAAG,CACjB,GAAGA,kBAAkB,EACrB;QACIhF,KAAK,EAAEqD,UAAU;QACjBhE,KAAK,EAAE8F,8BAA8B,aAA9BA,8BAA8B,uBAA9BA,8BAA8B,CAAE9F,KAAK;QAC5CkG,SAAS,EAAE,CACP;UACIvF,KAAK,EAAEiE,sBAAsB;UAC7B5E,KAAK,EAAE6E,sBAAsB;UAC7BoB,kBAAkB,EAAEjB;QACxB,CAAC;MAET,CAAC,CACJ;IACL;IACA,OAAOW,kBAAkB;EAC7B,CAAC;EACD,MAAM3C,yBAAyB,GAAIL,MAAM,IAAK;IAAA,IAAAwD,oBAAA,EAAAC,qBAAA;IAC1C,IAAIC,uBAAuB,GACvB,CAAA5I,QAAQ,aAARA,QAAQ,wBAAA0I,oBAAA,GAAR1I,QAAQ,CAAEqG,SAAS,cAAAqC,oBAAA,uBAAnBA,oBAAA,CAAqBG,yBAAyB,KAAI,EAAE;IACxD,MAAMtC,UAAU,GAAGrB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,gCAAgC;IAC3D,MAAM8C,oBAAoB,GAAG,wBAAwB7B,UAAU,EAAE;IACjE,MAAMuC,oBAAoB,GAAG,4BAA4BvC,UAAU,EAAE;IACrE,MAAMwC,sBAAsB,GAAG,8BAA8BxC,UAAU,EAAE;IACzE,MAAMyC,wBAAwB,GAC1B9D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,iCAAiCqB,UAAU,EAAE,CAAC;IAC3D,MAAMY,sBAAsB,GACxBjC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMhB,sBAAsB,GACxBlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMa,oBAAoB,GAAG/D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG4D,oBAAoB,CAAC;IAC3D,MAAMI,sBAAsB,GAAGhE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG6D,sBAAsB,CAAC;IAC/D,MAAMV,8BAA8B,IAAAM,qBAAA,GAAGC,uBAAuB,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBzE,IAAI,CAC/DoE,oBAAoB,IAAK;MACtB,OAAOA,oBAAoB,CAACpF,KAAK,IAAIqD,UAAU;IACnD,CACJ,CAAC;IACD,MAAM4C,mBAAmB,GAAGhC,sBAAsB,CAACM,KAAK,CAAC,YAAY,CAAC;IACtE,MAAMF,gBAAgB,GAAGL,mBAAmB,CAAC;MACzCC,sBAAsB;MACtBC;IACJ,CAAC,CAAC;IACF,IAAIiB,8BAA8B,EAAE;MAChCO,uBAAuB,CAACf,OAAO,CAAEU,uBAAuB,IAAK;QACzD,IAAIA,uBAAuB,CAACrF,KAAK,IAAIqD,UAAU,EAAE;UAC7CgC,uBAAuB,CAAC,WAAW,CAAC,GAAG,CACnC,GAAGA,uBAAuB,CAAC,WAAW,CAAC,EACvC;YACIrF,KAAK,EAAEiE,sBAAsB;YAC7B5E,KAAK,EAAE6E,sBAAsB;YAC7BgC,gBAAgB,EACZC,iCAAiC,CAC7BF,mBACJ,CAAC;YACLG,GAAG,EAAEL,oBAAoB;YACzBM,KAAK,EAAEL,sBAAsB;YAC7BM,aAAa,EAAER,wBAAwB;YACvCR,kBAAkB,EAAEjB;UACxB,CAAC,CACJ;QACL;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACHqB,uBAAuB,GAAG,CACtB,GAAGA,uBAAuB,EAC1B;QACI1F,KAAK,EAAEqD,UAAU;QACjBhE,KAAK,EAAE8F,8BAA8B,aAA9BA,8BAA8B,uBAA9BA,8BAA8B,CAAE9F,KAAK;QAC5CkG,SAAS,EAAE,CACP;UACIvF,KAAK,EAAEiE,sBAAsB;UAC7B5E,KAAK,EAAE6E,sBAAsB;UAC7BgC,gBAAgB,EACZC,iCAAiC,CAC7BF,mBACJ,CAAC;UACLG,GAAG,EAAEL,oBAAoB;UACzBM,KAAK,EAAEL,sBAAsB;UAC7BM,aAAa,EAAER,wBAAwB;UACvCR,kBAAkB,EAAEjB;QACxB,CAAC;MAET,CAAC,CACJ;IACL;IACA,OAAOqB,uBAAuB;EAClC,CAAC;EAED,MAAMpD,2BAA2B,GAAIN,MAAM,IAAK;IAAA,IAAAuE,oBAAA;IAC5C,IAAIC,+BAA+B,GAC/B,CAAA1J,QAAQ,aAARA,QAAQ,wBAAAyJ,oBAAA,GAARzJ,QAAQ,CAAEqG,SAAS,cAAAoD,oBAAA,uBAAnBA,oBAAA,CAAqBE,kCAAkC,KAAI,EAAE;IACjE,MAAMvB,oBAAoB,GAAG,4BAA4B;IACzD,MAAMjB,sBAAsB,GACxBjC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMhB,sBAAsB,GACxBlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMY,wBAAwB,GAC1B9D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,WAAW,CAAC;IAChD,MAAMc,sBAAsB,GACxBhE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMa,oBAAoB,GAAG/D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,MAAM,CAAC;IACpE,MAAMe,mBAAmB,GAAGhC,sBAAsB,CAACM,KAAK,CAAC,YAAY,CAAC;IACtEiC,+BAA+B,GAAG,CAC9B,GAAGA,+BAA+B,EAClC;MACIxG,KAAK,EAAEiE,sBAAsB;MAC7B5E,KAAK,EAAE6E,sBAAsB;MAC7BgC,gBAAgB,EACZC,iCAAiC,CAACF,mBAAmB,CAAC;MAC1DK,aAAa,EAAER,wBAAwB;MACvCM,GAAG,EAAEL,oBAAoB;MACzBM,KAAK,EAAEL;IACX,CAAC,CACJ;IAED,OAAOQ,+BAA+B;EAC1C,CAAC;EACD,MAAMhE,sBAAsB,GAAIR,MAAM,IAAK;IAAA,IAAA0E,oBAAA;IACvC,IAAIC,0BAA0B,GAC1B,CAAA7J,QAAQ,aAARA,QAAQ,wBAAA4J,oBAAA,GAAR5J,QAAQ,CAAEqG,SAAS,cAAAuD,oBAAA,uBAAnBA,oBAAA,CAAqBE,6BAA6B,KAAI,EAAE;IAC5D,MAAM1B,oBAAoB,GAAG,uBAAuB;IACpD,MAAMjB,sBAAsB,GACxBjC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMhB,sBAAsB,GACxBlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMY,wBAAwB,GAC1B9D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,WAAW,CAAC;IAChDyB,0BAA0B,GAAG,CACzB,GAAGA,0BAA0B,EAC7B;MACI3G,KAAK,EAAEiE,sBAAsB;MAC7B5E,KAAK,EAAE6E,sBAAsB;MAC7BoC,aAAa,EAAER;IACnB,CAAC,CACJ;IAED,OAAOa,0BAA0B;EACrC,CAAC;EAED,MAAMpE,sBAAsB,GAAIP,MAAM,IAAK;IAAA,IAAA6E,oBAAA,EAAAC,qBAAA;IACvC,IAAIC,uBAAuB,GACvB,CAAAjK,QAAQ,aAARA,QAAQ,wBAAA+J,oBAAA,GAAR/J,QAAQ,CAAEqG,SAAS,cAAA0D,oBAAA,uBAAnBA,oBAAA,CAAqBG,0BAA0B,KAAI,EAAE;IACzD,MAAM3D,UAAU,GAAG,SAAS;IAC5B,MAAM6B,oBAAoB,GAAG,4BAA4B7B,UAAU,EAAE;IACrE,MAAMY,sBAAsB,GACxBjC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMhB,sBAAsB,GACxBlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,GAAGkD,oBAAoB,QAAQ,CAAC;IAC7C,MAAMC,8BAA8B,IAAA2B,qBAAA,GAAGC,uBAAuB,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyB9F,IAAI,CAC/DoE,oBAAoB,IAAK;MACtB,OAAOA,oBAAoB,CAACpF,KAAK,IAAIqD,UAAU;IACnD,CACJ,CAAC;IACD,MAAM4C,mBAAmB,GAAGhC,sBAAsB,CAACM,KAAK,CAAC,YAAY,CAAC;IACtE,IAAIY,8BAA8B,EAAE;MAChC4B,uBAAuB,CAACpC,OAAO,CAAEU,uBAAuB,IAAK;QACzD,IAAIA,uBAAuB,CAACrF,KAAK,IAAIqD,UAAU,EAAE;UAC7CgC,uBAAuB,CAAC,WAAW,CAAC,GAAG,CACnC,GAAGA,uBAAuB,CAAC,WAAW,CAAC,EACvC;YACIrF,KAAK,EAAEiE,sBAAsB;YAC7B5E,KAAK,EAAE6E;UACX,CAAC,CACJ;QACL;MACJ,CAAC,CAAC;IACN,CAAC,MAAM;MACH6C,uBAAuB,GAAG,CACtB,GAAGA,uBAAuB,EAC1B;QACI/G,KAAK,EAAEqD,UAAU;QACjBhE,KAAK,EAAE8F,8BAA8B,aAA9BA,8BAA8B,uBAA9BA,8BAA8B,CAAE9F,KAAK;QAC5CkG,SAAS,EAAE,CACP;UACIvF,KAAK,EAAEiE,sBAAsB;UAC7B5E,KAAK,EAAE6E;QACX,CAAC;MAET,CAAC,CACJ;IACL;IACA,OAAO6C,uBAAuB;EAClC,CAAC;EAED,MAAMZ,iCAAiC,GAAIc,SAAS,IAAK;IACrD,MAAMC,8BAA8B,GAAG,CAAC,CAAC;IACzC,IAAI,CAAAD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,MAAM,IAAG,CAAC,EAAE;MACvBF,SAAS,CAACtC,OAAO,CAAC,CAACyC,cAAc,EAAEvC,KAAK,KAAK;QACzCqC,8BAA8B,CAAC,cAAcrC,KAAK,GAAG,CAAC,GAClDuC,cAAc;MACtB,CAAC,CAAC;IACN;IACA,OAAOF,8BAA8B;EACzC,CAAC;EAED,MAAMG,iCAAiC,GAAGA,CAAA,KAAM;IAAA,IAAAC,iBAAA;IAC5C,IAAI3K,OAAO,aAAPA,OAAO,wBAAA2K,iBAAA,GAAP3K,OAAO,CAAEgF,OAAO,cAAA2F,iBAAA,uBAAhBA,iBAAA,CAAkBhE,aAAa,CAAC,6BAA6B,CAAC,EAAE;MAChE5F,+BAA+B,CAAC,IAAI,CAAC;IACzC,CAAC,MAAM;MACH/B,OAAO,CAAC4L,IAAI,CAAC,wBAAwB,CAAC;IAC1C;EACJ,CAAC;EACD,MAAMC,oCAAoC,GAAGA,CAAA,KAAM;IAAA,IAAAC,iBAAA;IAC/C,IACI9K,OAAO,aAAPA,OAAO,wBAAA8K,iBAAA,GAAP9K,OAAO,CAAEgF,OAAO,cAAA8F,iBAAA,uBAAhBA,iBAAA,CAAkBnE,aAAa,CAAC,kCAAkC,CAAC,EACrE;MACE1F,oCAAoC,CAAC,IAAI,CAAC;IAC9C,CAAC,MAAM;MACHjC,OAAO,CAAC4L,IAAI,CAAC,wBAAwB,CAAC;IAC1C;EACJ,CAAC;EAED,MAAMG,qCAAqC,GAAGA,CAAA,KAAM;IAAA,IAAAC,iBAAA;IAChD,MAAMtE,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAAgL,iBAAA,GAAPhL,OAAO,CAAEgF,OAAO,cAAAgG,iBAAA,uBAAhBA,iBAAA,CAAkBrE,aAAa,CAC9C,6BACJ,CAAC;IACD,MAAMsE,+BAA+B,GAAG;MACpCC,mBAAmB,EAAE,CACjB,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,gBAAgB,CACnB;MACDC,YAAY,EAAE,CAAC,gBAAgB,CAAC;MAChCC,gBAAgB,EAAE,CAAC,gBAAgB;IACvC,CAAC;IACD,MAAMC,+BAA+B,GAAG;MACpCH,mBAAmB,EAAE,CACjB,eAAe,EACf,8BAA8B,EAC9B,mBAAmB,EACnB,cAAc,CACjB;MACDC,YAAY,EAAE,CAAC,eAAe,CAAC;MAC/BC,gBAAgB,EAAE,CAAC,eAAe;IACtC,CAAC;IACD,MAAME,4CAA4C,GAC9C9L,sBAAsB,CAAC0D,GAAG,CAAEqI,wBAAwB,IAAK;MACrD,IAAIC,yBAAyB,GACzBP,+BAA+B,CAACvE,UAAU,CAAC,IAAI,EAAE;MACrD,IAAI+E,yBAAyB,GACzBJ,+BAA+B,CAAC3E,UAAU,CAAC,IAAI,EAAE;MAErD6E,wBAAwB,CAAC3C,SAAS,CAACZ,OAAO,CACrC0D,4BAA4B,IAAK;QAC9B,MAAMC,mBAAmB,GACrBD,4BAA4B,CAACrI,KAAK;QACtC,MAAMuI,mBAAmB,GACrBF,4BAA4B,CAAChJ,KAAK;QAEtC,MAAM4G,mBAAmB,GACrBqC,mBAAmB,CAAC/D,KAAK,CAAC,YAAY,CAAC;QAC3C,MAAMiE,mBAAmB,GACrBD,mBAAmB,CAAChE,KAAK,CAAC,YAAY,CAAC;QAE3C,IAAI0B,mBAAmB,EAAE;UACrB,MAAMwC,KAAK,GAAGxC,mBAAmB,CAACyC,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;UACDR,yBAAyB,GAAG,CACxB,GAAG,IAAIS,GAAG,CAAC,CACP,GAAGT,yBAAyB,EAC5B,GAAGM,KAAK,CACX,CAAC,CACL;QACL;QACA,IAAID,mBAAmB,EAAE;UACrB,MAAMC,KAAK,GAAGD,mBAAmB,CAACE,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;UACDP,yBAAyB,GAAG,CACxB,GAAG,IAAIQ,GAAG,CAAC,CACP,GAAGR,yBAAyB,EAC5B,GAAGK,KAAK,CACX,CAAC,CACL;QACL;MACJ,CACJ,CAAC;MAED,OAAO;QACHzI,KAAK,EAAEkI,wBAAwB,CAAClI,KAAK;QACrCX,KAAK,EAAE6I,wBAAwB,CAAC7I,KAAK;QACrCwJ,mBAAmB,EAAEV,yBAAyB;QAC9CW,yBAAyB,EAAEV;MAC/B,CAAC;IACL,CAAC,CAAC;IAEN,OAAOH,4CAA4C;EACvD,CAAC;EACD,MAAMc,+CAA+C,GAAGA,CAAA,KAAM;IAAA,IAAAC,iBAAA;IAC1D,MAAM3F,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAAqM,iBAAA,GAAPrM,OAAO,CAAEgF,OAAO,cAAAqH,iBAAA,uBAAhBA,iBAAA,CAAkB1F,aAAa,CAC9C,kCACJ,CAAC;IACD,MAAMsE,+BAA+B,GAAG;MACpCC,mBAAmB,EAAE,CACjB,gBAAgB,EAChB,mBAAmB,EACnB,gBAAgB,CACnB;MACDC,YAAY,EAAE,CAAC,gBAAgB,CAAC;MAChCC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC;MACpCkB,eAAe,EAAE,CAAC,QAAQ;IAC9B,CAAC;IACD,MAAMjB,+BAA+B,GAAG;MACpCH,mBAAmB,EAAE,CACjB,eAAe,EACf,mBAAmB,EACnB,cAAc,CACjB;MACDC,YAAY,EAAE,CAAC,eAAe,CAAC;MAC/BC,gBAAgB,EAAE,CAAC,eAAe,CAAC;MACnCkB,eAAe,EAAE,CAAC,QAAQ;IAC9B,CAAC;IACD,MAAMhB,4CAA4C,GAC9C7L,iCAAiC,CAACyD,GAAG,CAChCqI,wBAAwB,IAAK;MAC1B,IAAIC,yBAAyB,GACzBP,+BAA+B,CAACvE,UAAU,CAAC,IAAI,EAAE;MACrD,IAAI+E,yBAAyB,GACzBJ,+BAA+B,CAAC3E,UAAU,CAAC,IAAI,EAAE;MAErD6E,wBAAwB,CAAC3C,SAAS,CAACZ,OAAO,CACrC0D,4BAA4B,IAAK;QAC9B,MAAMC,mBAAmB,GACrBD,4BAA4B,CAACrI,KAAK;QACtC,MAAMuI,mBAAmB,GACrBF,4BAA4B,CAAChJ,KAAK;QAEtC,MAAM4G,mBAAmB,GACrBqC,mBAAmB,CAAC/D,KAAK,CAAC,YAAY,CAAC;QAC3C,MAAMiE,mBAAmB,GACrBD,mBAAmB,CAAChE,KAAK,CAAC,YAAY,CAAC;QAE3C,IAAI0B,mBAAmB,EAAE;UACrB,MAAMwC,KAAK,GAAGxC,mBAAmB,CAACyC,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;UACDR,yBAAyB,GAAG,CACxB,GAAG,IAAIS,GAAG,CAAC,CACP,GAAGT,yBAAyB,EAC5B,GAAGM,KAAK,CACX,CAAC,CACL;QACL;QACA,IAAID,mBAAmB,EAAE;UACrB,MAAMC,KAAK,GAAGD,mBAAmB,CAACE,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;UACDP,yBAAyB,GAAG,CACxB,GAAG,IAAIQ,GAAG,CAAC,CACP,GAAGR,yBAAyB,EAC5B,GAAGK,KAAK,CACX,CAAC,CACL;QACL;MACJ,CACJ,CAAC;MAED,OAAO;QACHzI,KAAK,EAAEkI,wBAAwB,CAAClI,KAAK;QACrCX,KAAK,EAAE6I,wBAAwB,CAAC7I,KAAK;QACrCwJ,mBAAmB,EAAEV,yBAAyB;QAC9CW,yBAAyB,EAAEV;MAC/B,CAAC;IACL,CACJ,CAAC;IAEL,OAAOH,4CAA4C;EACvD,CAAC;EACD,MAAMiB,mCAAmC,GAAGA,CAAA,KAAM;IAC9C,MAAMC,+CAA+C,GACjD7M,uCAAuC,CAACuD,GAAG,CACtCqI,wBAAwB,IAAK;MAC1B,IAAIC,yBAAyB,GAAG,EAAE;MAClC,IAAIC,yBAAyB,GAAG,EAAE;MAElC,MAAME,mBAAmB,GAAGJ,wBAAwB,CAAClI,KAAK;MAC1D,MAAMuI,mBAAmB,GAAGL,wBAAwB,CAAC7I,KAAK;MAE1D,MAAM4G,mBAAmB,GACrBqC,mBAAmB,CAAC/D,KAAK,CAAC,YAAY,CAAC;MAC3C,MAAMiE,mBAAmB,GACrBD,mBAAmB,CAAChE,KAAK,CAAC,YAAY,CAAC;MAE3C,IAAI0B,mBAAmB,EAAE;QACrB,MAAMwC,KAAK,GAAGxC,mBAAmB,CAACyC,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;QACDR,yBAAyB,GAAG,CACxB,GAAG,IAAIS,GAAG,CAAC,CACP,GAAGT,yBAAyB,EAC5B,GAAGM,KAAK,CACX,CAAC,CACL;MACL;MACA,IAAID,mBAAmB,EAAE;QACrB,MAAMC,KAAK,GAAGD,mBAAmB,CAACE,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;QACDP,yBAAyB,GAAG,CACxB,GAAG,IAAIQ,GAAG,CAAC,CACP,GAAGR,yBAAyB,EAC5B,GAAGK,KAAK,CACX,CAAC,CACL;MACL;MAEA,OAAO;QACHI,mBAAmB,EAAEV,yBAAyB;QAC9CW,yBAAyB,EAAEV;MAC/B,CAAC;IACL,CACJ,CAAC;IAEL,OAAOe,+CAA+C;EAC1D,CAAC;EACD,MAAMC,0CAA0C,GAAGA,CAAA,KAAM;IACrD,MAAMC,uCAAuC,GACzChN,oBAAoB,CAACwD,GAAG,CAAEqI,wBAAwB,IAAK;MACnD,IAAIC,yBAAyB,GAAG,EAAE;MAClC,IAAIC,yBAAyB,GAAG,EAAE;MAElCF,wBAAwB,CAAC3C,SAAS,CAACZ,OAAO,CACrC0D,4BAA4B,IAAK;QAC9B,MAAMC,mBAAmB,GACrBD,4BAA4B,CAACrI,KAAK;QACtC,MAAMuI,mBAAmB,GACrBF,4BAA4B,CAAChJ,KAAK;QAEtC,MAAM4G,mBAAmB,GACrBqC,mBAAmB,CAAC/D,KAAK,CAAC,YAAY,CAAC;QAC3C,MAAMiE,mBAAmB,GACrBD,mBAAmB,CAAChE,KAAK,CAAC,YAAY,CAAC;QAE3C,IAAI0B,mBAAmB,EAAE;UACrB,MAAMwC,KAAK,GAAGxC,mBAAmB,CAACyC,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;UACDR,yBAAyB,GAAG,CACxB,GAAG,IAAIS,GAAG,CAAC,CACP,GAAGT,yBAAyB,EAC5B,GAAGM,KAAK,CACX,CAAC,CACL;QACL;QACA,IAAID,mBAAmB,EAAE;UACrB,MAAMC,KAAK,GAAGD,mBAAmB,CAACE,MAAM,CACnCnE,KAAK,IAAK,CAACA,KAAK,CAACoE,QAAQ,CAAC,YAAY,CAC3C,CAAC;UACDP,yBAAyB,GAAG,CACxB,GAAG,IAAIQ,GAAG,CAAC,CACP,GAAGR,yBAAyB,EAC5B,GAAGK,KAAK,CACX,CAAC,CACL;QACL;MACJ,CACJ,CAAC;MAED,OAAO;QACHzI,KAAK,EAAEkI,wBAAwB,CAAClI,KAAK;QACrCX,KAAK,EAAE6I,wBAAwB,CAAC7I,KAAK;QACrCwJ,mBAAmB,EAAEV,yBAAyB;QAC9CW,yBAAyB,EAAEV;MAC/B,CAAC;IACL,CAAC,CAAC;IAEN,OAAOiB,uCAAuC;EAClD,CAAC;EAED,MAAMC,kCAAkC,GAAGA,CAACC,IAAI,EAAEvJ,KAAK,EAAEwJ,QAAQ,KAAK;IAClE,MAAMlL,MAAM,GAAGiL,IAAI,CAACjL,MAAM;IAC1B,MAAMmL,YAAY,GAAGF,IAAI,CAACE,YAAY;IACtC,MAAMC,YAAY,GAAGH,IAAI,CAACG,YAAY;IACtC,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,0BAA0B;IAC9B,IAAIH,YAAY,EAAE;MACdG,0BAA0B,GAAG5J,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuE,KAAK,CAAC,YAAY,CAAC;MACvD,IACKxG,yBAAyB,IAAI,cAAc,IACxCI,4BAA4B,IAAInB,SAAS,IAC7CmB,4BAA4B,IAAI,kBAAkB,IACjDJ,yBAAyB,IAAI,mBAAmB,IAC7CE,iCAAiC,IAAIjB,SAAU,IACnDiB,iCAAiC,IAAI,uBAAuB,EAC9D;QAAA,IAAA4L,qBAAA;QACED,0BAA0B,IAAAC,qBAAA,GAAGD,0BAA0B,cAAAC,qBAAA,uBAA1BA,qBAAA,CAA4BnB,MAAM,CAC1DoB,WAAW,IAAK,CAAC,oBAAoB,CAACC,IAAI,CAACD,WAAW,CAAC,CAAC;QAC7D,CAAC;MACL;IACJ;IACA,IAAIJ,YAAY,EAAE;MACd,IACK3L,yBAAyB,IAAI,cAAc,IACxCI,4BAA4B,IAAInB,SAAS,IAC7CmB,4BAA4B,IAAI,kBAAkB,IACjDJ,yBAAyB,IAAI,mBAAmB,IAC7CE,iCAAiC,IAAIjB,SAAU,IACnDiB,iCAAiC,IAAI,uBAAuB,EAC9D;QACE2L,0BAA0B,GAAG5J,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuE,KAAK,CACrC,2BACJ,CAAC;MACL,CAAC,MAAM;QACHqF,0BAA0B,GAAG5J,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuE,KAAK,CAAC,YAAY,CAAC;MAC3D;IACJ;IACA;IACA,IAAIqF,0BAA0B,EAAE;MAC5BD,SAAS,GAAG,CAACC,0BAA0B,CAACI,IAAI,CACvCC,OAAO,IAAK,CAAC3L,MAAM,CAACqK,QAAQ,CAACsB,OAAO,CACzC,CAAC;IACL;IACA,IAAIN,SAAS,EAAE;MACXH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,MAAM;MACHA,QAAQ,CAAC,yDAAyD,CAAC,CAAC,CAAC;IACzE;EACJ,CAAC;EAED,MAAMU,0BAA0B,GAAGA,CAAA,KAAM;IAAA,IAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA;IACrC,IAAIC,uCAAuC,GACvC9C,qCAAqC,CAAC,CAAC;IAC3C,MAAMrE,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAAwN,iBAAA,GAAPxN,OAAO,CAAEgF,OAAO,cAAAwI,iBAAA,uBAAhBA,iBAAA,CAAkB7G,aAAa,CAC9C,6BACJ,CAAC;IACD,MAAMmH,oCAAoC,GACtCD,uCAAuC,aAAvCA,uCAAuC,uBAAvCA,uCAAuC,CAAExJ,IAAI,CACxC0J,8BAA8B,IAAK;MAAA,IAAAC,iBAAA;MAChC,OACID,8BAA8B,CAAC1K,KAAK,KACpCrD,OAAO,aAAPA,OAAO,wBAAAgO,iBAAA,GAAPhO,OAAO,CAAEgF,OAAO,cAAAgJ,iBAAA,uBAAhBA,iBAAA,CAAkBrH,aAAa,CAC3B,6BACJ,CAAC;IAET,CACJ,CAAC;IAEL,MAAMvE,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAInB,yBAAyB,IAAI,cAAc,GACzC,CACI;QACIoB,GAAG,EAAE,6BAA6B;QAClCE,KAAK,EAAE,aAAa;QACpBD,MAAM,EAAE,QAAQ;QAChBwL,WAAW,EAAE,oBAAoB;QACjCtJ,QAAQ,EAAGtB,KAAK,IAAK;UACjB6D,kBAAkB,CAACR,UAAU,EAAErD,KAAK,CAAC;UACrCzC,WAAW,CAAC,CAAC;QACjB,CAAC;QACD4D,OAAO,EAAEqJ,uCAAuC;QAChDjL,WAAW,EAAE;UACTC,UAAU,EAAE,IAAI;UAChB4B,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE;QACtB;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT;QACIlC,GAAG,EAAE,OAAO;QACZ0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA,CAACxP,MAAM;YACHyP,OAAO,EAAE1D,iCAAkC;YAC3C7F,IAAI,EAAC,SAAS;YAAAwJ,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GACjB,OAEO,CAAC;QAEjB;MACJ,CAAC,EACD,IAAItN,yBAAyB,IAAI,cAAc,KAC9CI,4BAA4B,IAAInB,SAAS,IACtCmB,4BAA4B,IAAI,kBAAkB,CAAC,IACvDV,4BAA4B,KAC5Bd,OAAO,aAAPA,OAAO,wBAAAyN,iBAAA,GAAPzN,OAAO,CAAEgF,OAAO,cAAAyI,iBAAA,uBAAhBA,iBAAA,CAAkB9G,aAAa,CAAC,6BAA6B,CAAC,IACxD,CACI;QACInE,GAAG,EAAE,iBAAiB;QACtB0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAGD,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE5B,mBAAmB,CAAChJ,GAAG,CAC1D,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIvN,yBAAyB,IAAI,cAAc,KAC9CI,4BAA4B,IAAInB,SAAS,IACtCmB,4BAA4B,IAAI,kBAAkB,CAAC,IACvDV,4BAA4B,KAC5Bd,OAAO,aAAPA,OAAO,wBAAA0N,iBAAA,GAAP1N,OAAO,CAAEgF,OAAO,cAAA0I,iBAAA,uBAAhBA,iBAAA,CAAkB/G,aAAa,CAAC,6BAA6B,CAAC,IACxD,CACI;QACInE,GAAG,EAAE,eAAesL,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEzK,KAAK,QAAQ;QACvEX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAEQZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEpL,KAE3C,CACD,CACT;QACDkC,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEmM,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE5B,mBAAmB;UACjEY,YAAY,EAAE,IAAI;UAClB9N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIoC,yBAAyB,IAAI,cAAc,KAC9CI,4BAA4B,IAAInB,SAAS,IACtCmB,4BAA4B,IAAI,kBAAkB,CAAC,IACvDV,4BAA4B,KAC5Bd,OAAO,aAAPA,OAAO,wBAAA2N,iBAAA,GAAP3N,OAAO,CAAEgF,OAAO,cAAA2I,iBAAA,uBAAhBA,iBAAA,CAAkBhH,aAAa,CAAC,6BAA6B,CAAC,IACxD,CACI;QACInE,GAAG,EAAE,iBAAiB;QACtB0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAGD,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE3B,yBAAyB,CAACjJ,GAAG,CAChE,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIvN,yBAAyB,IAAI,cAAc,KAC9CI,4BAA4B,IAAInB,SAAS,IACtCmB,4BAA4B,IAAI,kBAAkB,CAAC,IACvDV,4BAA4B,KAC5Bd,OAAO,aAAPA,OAAO,wBAAA4N,kBAAA,GAAP5N,OAAO,CAAEgF,OAAO,cAAA4I,kBAAA,uBAAhBA,kBAAA,CAAkBjH,aAAa,CAAC,6BAA6B,CAAC,IACxD,CACI;QACInE,GAAG,EAAE,eAAesL,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEzK,KAAK,QAAQ;QACvEX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAEQZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEpL,KAE3C,CACD,CACT;QACDkC,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEmM,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE3B,yBAAyB;UACvEY,YAAY,EAAE,IAAI;UAClB/N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;IACD,OAAOoD,IAAI;EACf,CAAC;EACD,MAAM0M,+BAA+B,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IAC1C,IAAIzB,uCAAuC,GACvCzB,+CAA+C,CAAC,CAAC;IACrD,MAAM1F,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAA+O,kBAAA,GAAP/O,OAAO,CAAEgF,OAAO,cAAA+J,kBAAA,uBAAhBA,kBAAA,CAAkBpI,aAAa,CAC9C,kCACJ,CAAC;IACD,MAAMmH,oCAAoC,GACtCD,uCAAuC,aAAvCA,uCAAuC,uBAAvCA,uCAAuC,CAAExJ,IAAI,CACxC0J,8BAA8B,IAAK;MAAA,IAAAwB,kBAAA;MAChC,OACIxB,8BAA8B,CAAC1K,KAAK,KACpCrD,OAAO,aAAPA,OAAO,wBAAAuP,kBAAA,GAAPvP,OAAO,CAAEgF,OAAO,cAAAuK,kBAAA,uBAAhBA,kBAAA,CAAkB5I,aAAa,CAC3B,kCACJ,CAAC;IAET,CACJ,CAAC;IAEL,MAAMvE,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ,IAAInB,yBAAyB,IAAI,mBAAmB,GAC9C,CACI;QACIoB,GAAG,EAAE,kCAAkC;QACvCE,KAAK,EAAE,aAAa;QACpBD,MAAM,EAAE,QAAQ;QAChBwL,WAAW,EAAE,oBAAoB;QACjCtJ,QAAQ,EAAGtB,KAAK,IAAK;UACjB+D,kCAAkC,CAC9BV,UAAU,EACVrD,KACJ,CAAC;UACDzC,WAAW,CAAC,CAAC;QACjB,CAAC;QACD4D,OAAO,EAAEqJ,uCAAuC;QAChDjL,WAAW,EAAE;UACTC,UAAU,EAAE,IAAI;UAChB4B,UAAU,EAAE,IAAI;UAChBC,gBAAgB,EAAE;QACtB;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT;QACIlC,GAAG,EAAE,OAAO;QACZ0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA,CAACxP,MAAM;YACHyP,OAAO,EAAEvD,oCAAqC;YAC9ChG,IAAI,EAAC,SAAS;YAAAwJ,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GACjB,OAEO,CAAC;QAEjB;MACJ,CAAC,EACD,IAAItN,yBAAyB,IAAI,mBAAmB,KACnDE,iCAAiC,IAAIjB,SAAS,IAC3CiB,iCAAiC,IAC7B,uBAAuB,CAAC,IAChCN,iCAAiC,KACjChB,OAAO,aAAPA,OAAO,wBAAAgP,kBAAA,GAAPhP,OAAO,CAAEgF,OAAO,cAAAgK,kBAAA,uBAAhBA,kBAAA,CAAkBrI,aAAa,CAC3B,kCACJ,CAAC,IACK,CACI;QACInE,GAAG,EAAE,iCAAiCsL,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEzK,KAAK,EAAE;QACnFX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,mBACe,EAAC,GAAG,eACrBpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAEQZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEpL,KAE3C,CACD,CACT;QACDiC,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC;MACrC,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIQ,yBAAyB,IAAI,mBAAmB,KACnDE,iCAAiC,IAAIjB,SAAS,IAC3CiB,iCAAiC,IAC7B,uBAAuB,CAAC,IAChCN,iCAAiC,KACjChB,OAAO,aAAPA,OAAO,wBAAAiP,kBAAA,GAAPjP,OAAO,CAAEgF,OAAO,cAAAiK,kBAAA,uBAAhBA,kBAAA,CAAkBtI,aAAa,CAC3B,kCACJ,CAAC,IACK,CACI;QACInE,GAAG,EAAE,0BAA0B;QAC/B0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAGD,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE5B,mBAAmB,CAAChJ,GAAG,CAC1D,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIvN,yBAAyB,IAAI,mBAAmB,KACnDE,iCAAiC,IAAIjB,SAAS,IAC3CiB,iCAAiC,IAC7B,uBAAuB,CAAC,IAChCN,iCAAiC,KACjChB,OAAO,aAAPA,OAAO,wBAAAkP,kBAAA,GAAPlP,OAAO,CAAEgF,OAAO,cAAAkK,kBAAA,uBAAhBA,kBAAA,CAAkBvI,aAAa,CAC3B,kCACJ,CAAC,IACK,CACI;QACInE,GAAG,EAAE,wBAAwBsL,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEzK,KAAK,QAAQ;QAChFX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAEQZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEpL,KAE3C,CACD,CACT;QACDkC,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEmM,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE5B,mBAAmB;UACjEY,YAAY,EAAE,IAAI;UAClB9N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIoC,yBAAyB,IAAI,mBAAmB,KACnDE,iCAAiC,IAAIjB,SAAS,IAC3CiB,iCAAiC,IAC7B,uBAAuB,CAAC,IAChCN,iCAAiC,KACjChB,OAAO,aAAPA,OAAO,wBAAAmP,kBAAA,GAAPnP,OAAO,CAAEgF,OAAO,cAAAmK,kBAAA,uBAAhBA,kBAAA,CAAkBxI,aAAa,CAC3B,kCACJ,CAAC,IACK,CACI;QACInE,GAAG,EAAE,0BAA0B;QAC/B0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAGD,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE3B,yBAAyB,CAACjJ,GAAG,CAChE,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIvN,yBAAyB,IAAI,mBAAmB,KACnDE,iCAAiC,IAAIjB,SAAS,IAC3CiB,iCAAiC,IAC7B,uBAAuB,CAAC,IAChCN,iCAAiC,KACjChB,OAAO,aAAPA,OAAO,wBAAAoP,kBAAA,GAAPpP,OAAO,CAAEgF,OAAO,cAAAoK,kBAAA,uBAAhBA,kBAAA,CAAkBzI,aAAa,CAC3B,kCACJ,CAAC,IACK,CACI;QACInE,GAAG,EAAE,wBAAwBsL,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEzK,KAAK,QAAQ;QAChFX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAEQZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEpL,KAE3C,CACD,CACT;QACDkC,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEmM,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE3B,yBAAyB;UACvEY,YAAY,EAAE,IAAI;UAClB/N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIoC,yBAAyB,IAAI,mBAAmB,KACnDE,iCAAiC,IAAIjB,SAAS,IAC3CiB,iCAAiC,IAC7B,uBAAuB,CAAC,IAChCN,iCAAiC,KACjChB,OAAO,aAAPA,OAAO,wBAAAqP,kBAAA,GAAPrP,OAAO,CAAEgF,OAAO,cAAAqK,kBAAA,uBAAhBA,kBAAA,CAAkB1I,aAAa,CAC3B,kCACJ,CAAC,IACK,CACI;QACInE,GAAG,EAAE,4BAA4BsL,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEzK,KAAK,EAAE;QAC9EX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,0BACsB,EAAC,GAAG,eAC5BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAEQZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEpL,KAE3C,CACD;QAEV;QACA;QACA;QACA;QACA;QACA;MACJ,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAItB,yBAAyB,IAAI,mBAAmB,KACnDE,iCAAiC,IAAIjB,SAAS,IAC3CiB,iCAAiC,IAC7B,uBAAuB,CAAC,IAChCN,iCAAiC,KACjChB,OAAO,aAAPA,OAAO,wBAAAsP,kBAAA,GAAPtP,OAAO,CAAEgF,OAAO,cAAAsK,kBAAA,uBAAhBA,kBAAA,CAAkB3I,aAAa,CAC3B,kCACJ,CAAC,IACK,CACI;QACInE,GAAG,EAAE,8BAA8BsL,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEzK,KAAK,EAAE;QAChFX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAEQZ,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAEpL,KAE3C,CACD;QAEV;QACA;QACA;QACA;QACA;QACA;MACJ,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;IACD,OAAON,IAAI;EACf,CAAC;EAED,MAAMoN,yCAAyC,GAAGA,CAAA,KAAM;IACpD,MAAMC,qBAAqB,GAAG,CAC1B,aAAa,EACb,gBAAgB,EAChB,iBAAiB,CACpB;IACD,MAAMC,qBAAqB,GAAG,CAC1B,iBAAiB,EACjB,UAAU,EACV,iBAAiB,CACpB;IACD,MAAMtN,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ,IAAInB,yBAAyB,IAAI,cAAc,IAC/CI,4BAA4B,IAAI,uBAAuB,GACjD,CACI;QACIgB,GAAG,EAAE,gCAAgC;QACrCE,KAAK,eAAEpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,eAAmB,CAAC;QACjC/J,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC;MACrC,CAAC,CACJ,GACD,EAAE,CAAC,EACT;QACI4B,GAAG,EAAE,8BAA8B;QACnC0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAED,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRe,qBAAqB,CAACvM,GAAG,CACtB,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,EACD,IAAIvN,yBAAyB,IAAI,cAAc,IAC/CI,4BAA4B,IAAI,uBAAuB,GACjD,CACI;QACIgB,GAAG,EAAE,6BAA6B;QAClCE,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,mBAAoB,CACrB,CACT;QACD9J,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAE8N,qBAAqB;UAC7B3C,YAAY,EAAE,IAAI;UAClB9N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT;QACIwD,GAAG,EAAE,8BAA8B;QACnC0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAED,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRgB,qBAAqB,CAACxM,GAAG,CACtB,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,EACD,IAAIvN,yBAAyB,IAAI,cAAc,IAC/CI,4BAA4B,IAAI,uBAAuB,GACjD,CACI;QACIgB,GAAG,EAAE,6BAA6B;QAClCE,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,mBAAoB,CACrB,CACT;QACD9J,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAE+N,qBAAqB;UAC7B3C,YAAY,EAAE,IAAI;UAClB/N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;IACD,OAAOoD,IAAI;EACf,CAAC;EAED,MAAMuN,8CAA8C,GAAGA,CAAA,KAAM;IACzD,IAAI9B,uCAAuC,GACvCtB,mCAAmC,CAAC,CAAC;IACzC,MAAMqD,0CAA0C,GAC5C/B,uCAAuC,CAAC,CAAC,CAAC;IAE9C,MAAMzL,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ,IAAInB,yBAAyB,IAAI,mBAAmB,IACpDE,iCAAiC,IAAI,mBAAmB,GAClD,CACI;QACIkB,GAAG,EAAE,qCAAqC;QAC1CE,KAAK,eAAEpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,eAAmB,CAAC;QACjC/J,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC;MACrC,CAAC,CACJ,GACD,EAAE,CAAC,EACT;QACI4B,GAAG,EAAE,mCAAmC;QACxC0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAED,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRkB,0CAA0C,aAA1CA,0CAA0C,uBAA1CA,0CAA0C,CAAE1D,mBAAmB,CAAChJ,GAAG,CAChE,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,EACD,IAAIvN,yBAAyB,IAAI,mBAAmB,IACpDE,iCAAiC,IAAI,mBAAmB,GAClD,CACI;QACIkB,GAAG,EAAE,kCAAkC;QACvCE,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,mBAAoB,CACrB,CACT;QACD9J,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEiO,0CAA0C,aAA1CA,0CAA0C,uBAA1CA,0CAA0C,CAAE1D,mBAAmB;UACvEY,YAAY,EAAE,IAAI;UAClB9N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT;QACIwD,GAAG,EAAE,mCAAmC;QACxC0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAED,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRkB,0CAA0C,aAA1CA,0CAA0C,uBAA1CA,0CAA0C,CAAEzD,yBAAyB,CAACjJ,GAAG,CACtE,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,EACD,IAAIvN,yBAAyB,IAAI,mBAAmB,IACpDE,iCAAiC,IAAI,mBAAmB,GAClD,CACI;QACIkB,GAAG,EAAE,kCAAkC;QACvCE,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,mBAAoB,CACrB,CACT;QACD9J,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEiO,0CAA0C,aAA1CA,0CAA0C,uBAA1CA,0CAA0C,CAAEzD,yBAAyB;UAC7EY,YAAY,EAAE,IAAI;UAClB/N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIoC,yBAAyB,IAAI,mBAAmB,IACpDE,iCAAiC,IAAI,mBAAmB,GAClD,CACI;QACIkB,GAAG,EAAE,kCAAkC;QACvCE,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,mBAAoB,CACrB,CACT;QACD/J,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC;MACrC,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIQ,yBAAyB,IAAI,mBAAmB,IACpDE,iCAAiC,IAAI,mBAAmB,GAClD,CACI;QACIkB,GAAG,EAAE,gCAAgC;QACrCE,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,0BACsB,EAAC,GAAG,eAC5BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,mBAAoB,CACrB,CACT;QACD/J,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC;MACrC,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;IACD,OAAOwB,IAAI;EACf,CAAC;EAED,MAAMyN,kCAAkC,GAAGA,CAAA,KAAM;IAC7C,IAAIhC,uCAAuC,GACvCpB,0CAA0C,CAAC,CAAC;IAChD,MAAMqD,gCAAgC,GAClCjC,uCAAuC,aAAvCA,uCAAuC,uBAAvCA,uCAAuC,CAAExJ,IAAI,CACxC0J,8BAA8B,IAAK;MAChC,OAAOA,8BAA8B,CAAC1K,KAAK,IAAI,SAAS;IAC5D,CACJ,CAAC;IAEL,MAAMjB,IAAI,GAAG;MACTC,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,CACJ;QACIC,GAAG,EAAE,8BAA8B;QACnC0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAED,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRoB,gCAAgC,aAAhCA,gCAAgC,uBAAhCA,gCAAgC,CAAE5D,mBAAmB,CAAChJ,GAAG,CACtD,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,EACD,IAAIvN,yBAAyB,IAAI,cAAc,IAC/CI,4BAA4B,IAAI,uBAAuB,GACjD,CACI;QACIgB,GAAG,EAAE,4BAA4BsN,gCAAgC,aAAhCA,gCAAgC,uBAAhCA,gCAAgC,CAAEzM,KAAK,QAAQ;QAChFX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,cAAe,CAChB,CACT;QACD9J,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEmO,gCAAgC,aAAhCA,gCAAgC,uBAAhCA,gCAAgC,CAAE5D,mBAAmB;UAC7DY,YAAY,EAAE,IAAI;UAClB9N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC,EACT;QACIwD,GAAG,EAAE,8BAA8B;QACnC0L,MAAM,EAAEA,CAAA,KAAM;UACV,oBACI5P,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAK,6CAED,eAAApQ,KAAA,CAAA6P,aAAA;YAAAE,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAQ,CAAC,EACRoB,gCAAgC,aAAhCA,gCAAgC,uBAAhCA,gCAAgC,CAAE3D,yBAAyB,CAACjJ,GAAG,CAC5D,CAACyL,QAAQ,EAAEzG,KAAK,kBACZ5J,KAAA,CAAA6P,aAAA,CAACpP,GAAG;YACAyD,GAAG,EAAE0F,KAAM;YACX0G,KAAK,EAAC,QAAQ;YACdC,SAAS,EAAC,iBAAiB;YAAAR,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAE1BC,QACA,CAEb,CACC,CAAC;QAEd;MACJ,CAAC,EACD,IAAIvN,yBAAyB,IAAI,cAAc,IAC/CI,4BAA4B,IAAI,uBAAuB,GACjD,CACI;QACIgB,GAAG,EAAE,4BAA4BsN,gCAAgC,aAAhCA,gCAAgC,uBAAhCA,gCAAgC,CAAEzM,KAAK,QAAQ;QAChFX,KAAK,eACDpE,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAM,4BACwB,EAAC,GAAG,eAC9BpQ,KAAA,CAAA6P,aAAA;UAAAE,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAG,cAAe,CAChB,CACT;QACD9J,QAAQ,EAAE,IAAI;QACdD,QAAQ,EAAGtB,KAAK,IAAKzC,WAAW,CAAC,CAAC;QAClCkC,KAAK,EAAE,CACH;UACIC,SAAS,EACL4J,kCAAkC;UACtChL,MAAM,EAAEmO,gCAAgC,aAAhCA,gCAAgC,uBAAhCA,gCAAgC,CAAE3D,yBAAyB;UACnEY,YAAY,EAAE,IAAI;UAClB/N,OAAO,EACH;QACR,CAAC;MAET,CAAC,CACJ,GACD,EAAE,CAAC;IAEjB,CAAC;IACD,OAAOoD,IAAI;EACf,CAAC;EAED,MAAM2N,gCAAgC,GAAIC,OAAO,IAAK;IAClD3O,4BAA4B,CAAC2O,OAAO,CAAC;EACzC,CAAC;EAED,MAAMC,wCAAwC,GAAID,OAAO,IAAK;IAC1DzO,oCAAoC,CAACyO,OAAO,CAAC;EACjD,CAAC;EAED,MAAME,mCAAmC,GAAIF,OAAO,IAAK;IACrDvO,+BAA+B,CAACuO,OAAO,CAAC;EAC5C,CAAC;EAED,MAAMG,iBAAiB,GAAIH,OAAO,IAAK;IACnC7O,UAAU,CAAC6O,OAAO,CAAC;EACvB,CAAC;EAED,MAAMI,gCAAgC,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IAC3C,IAAI7J,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAAqQ,kBAAA,GAAPrQ,OAAO,CAAEgF,OAAO,cAAAqL,kBAAA,uBAAhBA,kBAAA,CAAkB1J,aAAa,CAC5C,6BACJ,CAAC;IACD,MAAMC,gBAAgB,GAAG,eAAeF,UAAU,QAAQ;IAC1D,MAAMG,gBAAgB,GAAG,eAAeH,UAAU,QAAQ;IAC1D,MAAM8J,aAAa,GAAGxQ,OAAO,aAAPA,OAAO,wBAAAsQ,kBAAA,GAAPtQ,OAAO,CAAEgF,OAAO,cAAAsL,kBAAA,uBAAhBA,kBAAA,CAAkB3J,aAAa,CAACC,gBAAgB,CAAC;IACvE,MAAM6J,aAAa,GAAGzQ,OAAO,aAAPA,OAAO,wBAAAuQ,kBAAA,GAAPvQ,OAAO,CAAEgF,OAAO,cAAAuL,kBAAA,uBAAhBA,kBAAA,CAAkB5J,aAAa,CAACE,gBAAgB,CAAC;IACvE,OAAO,CAACrF,4BAA4B,IAAInB,SAAS,IAC7CmB,4BAA4B,IAAI,kBAAkB,KAClDJ,yBAAyB,IAAI,cAAc,IAC3CoP,aAAa,IACbC,aAAa,GACX,IAAI,GACJ,KAAK;EACf,CAAC;EACD,MAAMC,sCAAsC,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IACjD,IAAInK,UAAU,GAAG1G,OAAO,aAAPA,OAAO,wBAAA2Q,kBAAA,GAAP3Q,OAAO,CAAEgF,OAAO,cAAA2L,kBAAA,uBAAhBA,kBAAA,CAAkBhK,aAAa,CAC5C,kCACJ,CAAC;IACD,MAAMC,gBAAgB,GAAG,wBAAwBF,UAAU,QAAQ;IACnE,MAAMG,gBAAgB,GAAG,wBAAwBH,UAAU,QAAQ;IACnE;IACA,MAAM8J,aAAa,GAAGxQ,OAAO,aAAPA,OAAO,wBAAA4Q,kBAAA,GAAP5Q,OAAO,CAAEgF,OAAO,cAAA4L,kBAAA,uBAAhBA,kBAAA,CAAkBjK,aAAa,CAACC,gBAAgB,CAAC;IACvE,MAAM6J,aAAa,GAAGzQ,OAAO,aAAPA,OAAO,wBAAA6Q,kBAAA,GAAP7Q,OAAO,CAAEgF,OAAO,cAAA6L,kBAAA,uBAAhBA,kBAAA,CAAkBlK,aAAa,CAACE,gBAAgB,CAAC;IACvE;IACA,OAAO,CAACvF,iCAAiC,IAAIjB,SAAS,IAClDiB,iCAAiC,IAAI,uBAAuB,KAC5DF,yBAAyB,IAAI,cAAc,IAC3CoP,aAAa,IACbC,aAAa,GACX,IAAI,GACJ,KAAK;EACf,CAAC;EAED,MAAMK,6CAA6C,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,kBAAA;IACxD,MAAMpK,gBAAgB,GAAG,kCAAkC;IAC3D,MAAMC,gBAAgB,GAAG,kCAAkC;IAC3D;IACA,MAAM2J,aAAa,GAAGxQ,OAAO,aAAPA,OAAO,wBAAA+Q,kBAAA,GAAP/Q,OAAO,CAAEgF,OAAO,cAAA+L,kBAAA,uBAAhBA,kBAAA,CAAkBpK,aAAa,CAACC,gBAAgB,CAAC;IACvE,MAAM6J,aAAa,GAAGzQ,OAAO,aAAPA,OAAO,wBAAAgR,kBAAA,GAAPhR,OAAO,CAAEgF,OAAO,cAAAgM,kBAAA,uBAAhBA,kBAAA,CAAkBrK,aAAa,CAACE,gBAAgB,CAAC;IACvE;IACA,OAAOvF,iCAAiC,IAAI,mBAAmB,IAC3DF,yBAAyB,IAAI,mBAAmB,IAChDoP,aAAa,IACbC,aAAa,GACX,IAAI,GACJ,KAAK;EACf,CAAC;EACD,MAAMQ,wCAAwC,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,kBAAA;IACnD,MAAMvK,gBAAgB,GAAG,6BAA6B;IACtD,MAAMC,gBAAgB,GAAG,6BAA6B;IACtD;IACA,MAAM2J,aAAa,GAAGxQ,OAAO,aAAPA,OAAO,wBAAAkR,kBAAA,GAAPlR,OAAO,CAAEgF,OAAO,cAAAkM,kBAAA,uBAAhBA,kBAAA,CAAkBvK,aAAa,CAACC,gBAAgB,CAAC;IACvE,MAAM6J,aAAa,GAAGzQ,OAAO,aAAPA,OAAO,wBAAAmR,kBAAA,GAAPnR,OAAO,CAAEgF,OAAO,cAAAmM,kBAAA,uBAAhBA,kBAAA,CAAkBxK,aAAa,CAACE,gBAAgB,CAAC;IACvE;IACAf,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEyK,aAAa,CAAC;IAC3C1K,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE0K,aAAa,CAAC;IAC3C3K,OAAO,CAACC,GAAG,CACP,8BAA8B,EAC9BvE,4BACJ,CAAC;IACDsE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE3E,yBAAyB,CAAC;IACnE,OAAOI,4BAA4B,IAAI,uBAAuB,IAC1DJ,yBAAyB,IAAI,cAAc,IAC3CoP,aAAa,IACbC,aAAa,GACX,IAAI,GACJ,KAAK;EACf,CAAC;EAED,MAAMW,wCAAwC,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,kBAAA;IACnD,IAAI5K,UAAU,GAAG,SAAS;IAC1B,MAAME,gBAAgB,GAAG,4BAA4BF,UAAU,QAAQ;IACvE,MAAMG,gBAAgB,GAAG,4BAA4BH,UAAU,QAAQ;IACvE,MAAM8J,aAAa,GAAGxQ,OAAO,aAAPA,OAAO,wBAAAqR,kBAAA,GAAPrR,OAAO,CAAEgF,OAAO,cAAAqM,kBAAA,uBAAhBA,kBAAA,CAAkB1K,aAAa,CAACC,gBAAgB,CAAC;IACvE,MAAM6J,aAAa,GAAGzQ,OAAO,aAAPA,OAAO,wBAAAsR,kBAAA,GAAPtR,OAAO,CAAEgF,OAAO,cAAAsM,kBAAA,uBAAhBA,kBAAA,CAAkB3K,aAAa,CAACE,gBAAgB,CAAC;IACvE,OAAOrF,4BAA4B,IAAI,uBAAuB,IAC1DJ,yBAAyB,IAAI,cAAc,IAC3CoP,aAAa,IACbC,aAAa,GACX,IAAI,GACJ,KAAK;EACf,CAAC;EACD3K,OAAO,CAACC,GAAG,CACP,0CAA0C,EAC1CkL,wCAAwC,CAAC,CAC7C,CAAC;EACD,oBACI3S,KAAA,CAAA6P,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKhO,iBAAiB,gBACdpC,KAAA,CAAA6P,aAAA;IAAKU,SAAS,EAAC,mCAAmC;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9CpQ,KAAA,CAAA6P,aAAA,CAACjP,gBAAgB;IAAAmP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAClB,CAAC,GACNvO,QAAQ,IAAIE,SAAS,gBACrB/B,KAAA,CAAA6P,aAAA;IAAGU,SAAS,EAAC,aAAa;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEpO,KAAS,CAAC,gBAEtChC,KAAA,CAAA6P,aAAA,CAAChP,MAAM;IAAAkP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACHpQ,KAAA,CAAA6P,aAAA,CAAA7P,KAAA,CAAAiT,QAAA,qBACIjT,KAAA,CAAA6P,aAAA,CAACvP,IAAI;IACDiQ,SAAS,EAAC,SAAS;IACnB2C,MAAM,EAAC,UAAU;IACjBC,GAAG,EAAEzR,OAAQ;IACb0R,QAAQ,EAAEtM,YAAa;IACvBuM,aAAa,EAAEpL,eAAgB;IAAA8H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/BpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI;IACD8S,gBAAgB,EAAC,UAAU;IAC3BjN,QAAQ,EAAGkN,SAAS,IAChB1B,iBAAiB,CAAC0B,SAAS,CAC9B;IAAAxD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEDpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,eAAEzT,KAAA,CAAA6P,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAM,UAAc,CAAE;IAC3BlM,GAAG,EAAC,UAAU;IACdwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElBpQ,KAAA,CAAA6P,aAAA,CAACzP,KAAK;IACFM,OAAO,EAAC,+HAA+H;IACvIiT,WAAW,EAAC,oFAAoF;IAChGpN,IAAI,EAAC,MAAM;IACXqN,QAAQ;IAAA7D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eACFpQ,KAAA,CAAA6P,aAAA,CAAClP,WAAW;IACRmD,IAAI,EAAEmC,eAAe,CAAC,CAAE;IACxB4N,IAAI,EAAEnS,OAAQ;IAAAqO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACjB,CACS,CAAC,eAEfpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,eAAEzT,KAAA,CAAA6P,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAM,gBAAoB,CAAE;IACjClM,GAAG,EAAC,eAAe;IACnBwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElBpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI;IACD8S,gBAAgB,EAAC,QAAQ;IACzBjN,QAAQ,EAAGkN,SAAS,IAChB9B,gCAAgC,CAC5B8B,SACJ,CACH;IAAAxD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEDpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,QAAQ;IACZvP,GAAG,EAAC,QAAQ;IACZwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElBpQ,KAAA,CAAA6P,aAAA,CAACzP;EACG;EAAA;IACAM,OAAO,EAAC,yBAAyB;IACjCiT,WAAW,EAAC,8MAA8M;IAC1NpN,IAAI,EAAC,MAAM;IACXqN,QAAQ;IAAA7D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eACFpQ,KAAA,CAAA6P,aAAA,CAAClP,WAAW;IACRmD,IAAI,EAAED,oBAAoB,CAAC,CAAE;IAC7BgQ,IAAI,EAAEnS,OAAQ;IAAAqO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACjB,CACS,CAAC,eACfpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,cAAc;IAClBvP,GAAG,EAAC,cAAc;IAClBwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElBpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI;IACD8S,gBAAgB,EAAC,kBAAkB;IACnCjN,QAAQ,EAAGkN,SAAS,IAChB3B,mCAAmC,CAC/B2B,SACJ,CACH;IAAAxD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEDpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,kBAAkB;IACtBvP,GAAG,EAAC,kBAAkB;IACtBwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGlBpQ,KAAA,CAAA6P,aAAA,CAAClP,WAAW;IACRkT,IAAI,EAAEnS,OAAQ;IACdoC,IAAI,EAAEmL,0BAA0B,CAAC,CAAE;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACtC,CACS,CAAC,eACfpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,uBAAuB;IAC3BvP,GAAG,EAAC,uBAAuB;IAC3BwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGlBpQ,KAAA,CAAA6P,aAAA,CAAClP,WAAW;IACRkT,IAAI,EAAEnS,OAAQ;IACdoC,IAAI,EAAEyN,kCAAkC,CAAC,CAAE;IAAAxB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC9C,CACS,CAAC,eACfpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,uBAAuB;IAC3BvP,GAAG,EAAC,uBAAuB;IAC3BwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGlBpQ,KAAA,CAAA6P,aAAA,CAAClP,WAAW;IACRkT,IAAI,EAAEnS,OAAQ;IACdoC,IAAI,EAAEoN,yCAAyC,CAAC,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrD,CACS,CACZ,CACI,CAAC,eACfpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,mBAAmB;IACvBvP,GAAG,EAAC,mBAAmB;IACvBwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElBpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI;IACD8S,gBAAgB,EAAC,uBAAuB;IACxCjN,QAAQ,EAAGkN,SAAS,IAChB5B,wCAAwC,CACpC4B,SACJ,CACH;IAAAxD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEDpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,sBAAsB;IAC1BvP,GAAG,EAAC,uBAAuB;IAC3BwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGlBpQ,KAAA,CAAA6P,aAAA,CAAClP,WAAW;IACRkT,IAAI,EAAEnS,OAAQ;IACdoC,IAAI,EAAE0M,+BAA+B,CAAC,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC3C,CACS,CAAC,eACfpQ,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,EAAC,mBAAmB;IACvBvP,GAAG,EAAC,mBAAmB;IACvBwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGlBpQ,KAAA,CAAA6P,aAAA,CAAClP,WAAW;IACRkT,IAAI,EAAEnS,OAAQ;IACdoC,IAAI,EAAEuN,8CAA8C,CAAC,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC1D,CACS,CACZ,CACI,CACZ,CACI,CAAC,EAEdnP,aAAa,CAAC6S,OAAO,CAAC,CAAC,iBACpB9T,KAAA,CAAA6P,aAAA,CAACrP,IAAI,CAACgT,OAAO;IACTC,GAAG,eAAEzT,KAAA,CAAA6P,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAM,mBAAuB,CAAE;IACpClM,GAAG,EAAC,mBAAmB;IACvBwP,WAAW,EAAE,IAAK;IAAA3D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElBpQ,KAAA,CAAA6P,aAAA,CAACvO,gBAAgB;IAACI,OAAO,EAAEA,OAAQ;IAAAqO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC3B,CAEhB,CAAC,EACN,CAACxN,OAAO,IAAI,eAAe,IACxBA,OAAO,IAAI,mBAAmB,IAC9BE,yBAAyB,IAAIf,SAAS,IACtCe,yBAAyB,IAAI,QAAQ,IACpCE,iCAAiC,IAC9BjB,SAAS,IACTiB,iCAAiC,IAC7B,uBAAuB,IAC3BA,iCAAiC,IAC7B,mBAAmB,IACvBF,yBAAyB,IACrB,cAAc,IAClBI,4BAA4B,IAAInB,SAAS,IACzCmB,4BAA4B,IACxB,kBAAkB,IACtBA,4BAA4B,IACxB,uBAAuB,IAC3BA,4BAA4B,IACxB,uBAAuB,IAC3BJ,yBAAyB,IACrB,mBAAoB,IAC5BgP,gCAAgC,CAAC,CAAC,IAClCM,sCAAsC,CAAC,CAAC,IACxCI,6CAA6C,CAAC,CAAC,IAC/CG,wCAAwC,CAAC,CAAC,IAC1CG,wCAAwC,CAAC,CAAC,kBAC1C9S,KAAA,CAAA6P,aAAA,CAACvP,IAAI,CAACyT,IAAI;IAACxD,SAAS,EAAC,aAAa;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BlO,gBAAgB,iBAAIlC,KAAA,CAAA6P,aAAA,CAACtP,IAAI;IAAAwP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC7BpQ,KAAA,CAAA6P,aAAA,CAACxP,MAAM;IACH2T,QAAQ,EAAC,QAAQ;IACjBzN,IAAI,EAAC,SAAS;IACdgK,SAAS,EAAC,SAAS;IACnB1J,QAAQ,EAAE3E,gBAAiB;IAAA6N,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B,QAEO,CACD,CAEb,CACR,CACE,CAEX,CAAC;AAEd,CAAC;AAED,eAAe3O,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}