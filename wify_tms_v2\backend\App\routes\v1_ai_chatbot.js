var express = require('express');
const { getUserContextFrmReq } = require('../api_models/utils/authrizor');
var router = express.Router();
const gai_cron_key = process.env.GAI_RATING_CRON_KEY;

router.get(`/`, function (req, res, next) {
    const model = setParamsToModel(req);
    console.log('req.query', req.query);
    console.log('req.params', req.params);
    console.log('req.user_details', req.user_details);
    model.initVaAiChatBot(req.query).then((resp) => {
        res.status(resp.httpStatus).send(resp.resp);
    });
});

router.post(`/brands-list`, function (req, res, next) {
    const model = setParamsToModel(req);
    console.log('brands-list :: req.query', req.query);
    console.log('brands-list :: req.params', req.params);
    console.log('brands-list :: req.body', req.body);
    model.getPossibleBrandLists(req.body).then((resp) => {
        res.status(resp.httpStatus).send(resp.resp);
    });
});

router.post(`/srvc-type-list`, function (req, res, next) {
    const model = setParamsToModel(req);
    console.log('srvc-type-list :: req.query', req.query);
    console.log('srvc-type-list :: req.params', req.params);
    console.log('srvc-type-list :: req.body', req.body);
    model.getPossibleBrandSrvcTypeLists(req.body).then((resp) => {
        res.status(resp.httpStatus).send(resp.resp);
    });
});

router.post(`/srvc-type-statuses`, function (req, res, next) {
    const model = setParamsToModel(req);
    console.log('srvc-type-statuses :: req.query', req.query);
    console.log('srvc-type-statuses :: req.params', req.params);
    console.log('srvc-type-statuses :: req.body', req.body);
    model.getPossibleBrandSrvcTypeStatuses(req.body).then((resp) => {
        res.status(resp.httpStatus).send(resp.resp);
    });
});

router.post(`/srvc-reqs`, function (req, res, next) {
    const model = setParamsToModel(req);
    console.log('srvc-reqs :: req.query', req.query);
    console.log('srvc-reqs :: req.params', req.params);
    console.log('srvc-reqs :: req.body', req.body);
    model.getPossibleSrvcRequests(req.body).then((resp) => {
        res.status(resp.httpStatus).send(resp.resp);
    });
});

router.post(`/srvc-reqs-modify`, function (req, res, next) {
    const model = setParamsToModel(req);
    console.log('srvc-reqs-modify :: req.query', req.query);
    console.log('srvc-reqs-modify :: req.params', req.params);
    console.log('srvc-reqs-modify :: req.body', req.body);
    model.getPossibleSrvcRequestsAndUpdate(req.body).then((resp) => {
        res.status(resp.httpStatus).send(resp.resp);
    });
});

const setParamsToModel = (req) => {
    const model = require('../api_models/va_ai_chatbot_model');
    model.db = req.app.get('db');
    model.databaseReplica = req.app.get('db_replica');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
    return model.getFreshInstance(model);
};

module.exports = router;
