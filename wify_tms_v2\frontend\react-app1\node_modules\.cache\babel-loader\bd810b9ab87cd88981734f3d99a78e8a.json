{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\WIFY\\\\AiChatBot\\\\index.js\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport './aichatbot.css';\nimport { <PERSON><PERSON>, Drawer, Toolt<PERSON> } from 'antd';\nimport { FiSend } from 'react-icons/fi';\nimport { SlArrowDownCircle } from 'react-icons/sl';\nimport { PiNotePencil } from 'react-icons/pi';\nimport { SlClose } from 'react-icons/sl';\nimport ConfigHelpers from '../../../util/ConfigHelpers';\nimport { isMobileView } from '../../../util/helpers';\nimport AgentMessage from './AgentMessage';\nimport UserMessage from './UserMessage';\nimport http_utils from '../../../util/http_utils';\nimport axios from 'axios';\nconst AiChatBot = ({\n  showAiChatBot,\n  setShowAiChatBot\n}) => {\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState('');\n  const [sessionId, setSessionId] = useState('');\n  const [isScrolledUp, setIsScrolledUp] = useState(false);\n  const [helpfulMap, setHelpfulMap] = useState({});\n\n  // Refs for scroll handling\n  const lastMsgInChat = useRef(null);\n  const chatContainerRef = useRef(null);\n  function initViewData(latestMessage) {\n    console.log('AiChatBot :: initViewData :: latestMessage', latestMessage);\n    const newChat = (messages === null || messages === void 0 ? void 0 : messages.length) == 0;\n    var params = {\n      latestMessage,\n      is_new_chat: newChat,\n      session_id: sessionId\n    };\n    const onComplete = resp => {\n      var _resp$data3;\n      console.log('AiChatBot :: initViewData :: onComplete :: resp data', resp.data);\n      setMessages(prev => prev.map(msg => {\n        var _resp$data, _resp$data2;\n        return msg.isGenerating ? {\n          ...msg,\n          text: resp === null || resp === void 0 ? void 0 : (_resp$data = resp.data) === null || _resp$data === void 0 ? void 0 : _resp$data.text,\n          isGenerating: false,\n          block: resp === null || resp === void 0 ? void 0 : (_resp$data2 = resp.data) === null || _resp$data2 === void 0 ? void 0 : _resp$data2.message\n        } : msg;\n      }));\n      setSessionId((resp === null || resp === void 0 ? void 0 : (_resp$data3 = resp.data) === null || _resp$data3 === void 0 ? void 0 : _resp$data3.session_id) || '');\n      scrollToBottom();\n    };\n    const onError = error => {\n      console.log('AiChatBot :: error ::', error);\n    };\n    http_utils.performGetCall('/ai-chatbot/', params, onComplete, onError);\n  }\n  // Auto-scroll on user message\n  useEffect(() => {\n    const lastMessage = messages[messages.length - 1];\n    if ((lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.from) === 'user' || (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.isGenerating)) {\n      const timeout = setTimeout(() => {\n        scrollToBottom();\n      }, 50);\n      return () => clearTimeout(timeout);\n    }\n  }, [messages]);\n\n  // Detect scroll position\n  useEffect(() => {\n    const container = chatContainerRef.current;\n    const handleScroll = () => {\n      const nearBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 100;\n      setIsScrolledUp(!nearBottom);\n    };\n    if (container) {\n      container.addEventListener('scroll', handleScroll);\n    }\n    // Check scroll position right after new message is added\n    if (container) {\n      const nearBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 100;\n      setIsScrolledUp(!nearBottom);\n    }\n    return () => {\n      if (container) {\n        container.removeEventListener('scroll', handleScroll);\n      }\n    };\n  }, [messages]);\n\n  // Scroll to bottom of message list\n  const scrollToBottom = () => {\n    if (chatContainerRef.current) {\n      // Check if any feedback section is currently open\n      const hasOpenFeedback = messages.some(msg => msg.showFeedbackOptions);\n      if (hasOpenFeedback) {\n        // If feedback section is open, scroll to the very bottom of the container\n        // to ensure the feedback section is visible\n        chatContainerRef.current.scrollTo({\n          top: chatContainerRef.current.scrollHeight,\n          behavior: 'smooth'\n        });\n      } else if (lastMsgInChat.current) {\n        // Normal scroll behavior when no feedback section is open\n        lastMsgInChat.current.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }\n    }\n  };\n\n  // Get formatted current time\n  const getCurrentTime = () => new Date().toLocaleTimeString([], {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  });\n\n  // Handle message send\n  const handleUserPrompt = () => {\n    console.log('AiChatBot :: handleUserPrompt', input);\n    if (!input.trim()) return;\n    const time = getCurrentTime();\n    const userMessage = {\n      from: 'user',\n      text: input,\n      time\n    };\n\n    // Add the user message to the chat immediately\n    setMessages(prev => [...prev, userMessage]);\n    setInput('');\n    // Show generating resp animation in chat\n    const generatingMsg = {\n      from: 'tms-ai',\n      text: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"wy-acb-initial-loading\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 19\n        }\n      }),\n      time,\n      isGenerating: true\n    };\n    setMessages(prev => [...prev, generatingMsg]);\n    initViewData(userMessage);\n  };\n  const handleNewChat = () => {\n    setMessages([]);\n    setSessionId('');\n  };\n  const renderChatView = () => /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-messages\",\n    ref: chatContainerRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }\n  }, messages.map((msg, idx) => {\n    if (msg.from === 'user') {\n      return /*#__PURE__*/React.createElement(UserMessage, {\n        key: idx,\n        message: msg,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 32\n        }\n      });\n    }\n    return /*#__PURE__*/React.createElement(AgentMessage, {\n      key: idx,\n      message: msg,\n      isGenerating: msg.isGenerating,\n      idx: idx,\n      helpfulMap: helpfulMap,\n      setHelpfulMap: setHelpfulMap,\n      messages: messages,\n      setMessages: setMessages,\n      sessionId: sessionId,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 25\n      }\n    });\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: lastMsgInChat,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 17\n    }\n  })), isScrolledUp && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: scrollToBottom,\n    className: \"wy-acb-jump-to-latest-button\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(SlArrowDownCircle, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 25\n    }\n  }), \" Jump to Latest\")), messages.length === 0 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-intro-message gx-text-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 21\n    }\n  }, \"Hello! \", ConfigHelpers.getFullUserName()), ' ', /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-intro-message-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 21\n    }\n  }, \"I am your TMS AI Assistant.\", /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 25\n    }\n  }), \"How can I help you with your TMS operations today?\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-prompt-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    placeholder: \"Type a message...\",\n    value: input\n    //disable text area if isGenerating is true\n    ,\n\n    onChange: e => setInput(e.target.value),\n    onKeyDown: e => {\n      const isGenerating = messages.some(msg => msg.isGenerating);\n      if (e.key === 'Enter' && !e.shiftKey) {\n        if (isGenerating) {\n          e.preventDefault(); // Block submit while generating\n        } else {\n          e.preventDefault(); // ✅ Prevents new line\n          handleUserPrompt();\n        }\n      }\n    },\n    className: \"wy-acb-prompt-textarea\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: handleUserPrompt,\n    className: \"wy-acb-send-button\",\n    disabled: messages.some(msg => msg.isGenerating),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FiSend, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 21\n    }\n  }))));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Drawer, {\n    placement: isMobileView() ? 'bottom' : 'right',\n    visible: showAiChatBot,\n    onClose: () => setShowAiChatBot(false),\n    className: \"wy-acb-drawer-wrapper\",\n    headerStyle: {\n      display: 'none'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-chat-container box\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-chat-header gx-d-flex gx-justify-content-between gx-align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"gx-mb-0 wy-acb-title-ai-assistant\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 25\n    }\n  }, \"Jiffy\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-d-flex gx-align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(Tooltip, {\n    title: \"Start New Chat\",\n    placement: \"bottom\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    size: \"small\",\n    onClick: handleNewChat,\n    className: \"gx-mr-0 gx-mb-0 wy-acb-new-chat-button gx-fs-18 gx-d-flex gx-align-items-center\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(PiNotePencil, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 37\n    }\n  }))))), renderChatView())));\n};\nexport default AiChatBot;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Drawer", "<PERSON><PERSON><PERSON>", "FiSend", "SlArrowDownCircle", "PiNotePencil", "SlClose", "ConfigHelpers", "isMobile<PERSON>iew", "AgentMessage", "UserMessage", "http_utils", "axios", "AiChatBot", "showAiChatBot", "setShowAiChatBot", "messages", "setMessages", "input", "setInput", "sessionId", "setSessionId", "isScrolledUp", "setIsScrolledUp", "helpfulMap", "setHelpfulMap", "lastMsgInChat", "chatContainerRef", "initViewData", "latestMessage", "console", "log", "newChat", "length", "params", "is_new_chat", "session_id", "onComplete", "resp", "_resp$data3", "data", "prev", "map", "msg", "_resp$data", "_resp$data2", "isGenerating", "text", "block", "message", "scrollToBottom", "onError", "error", "performGetCall", "lastMessage", "from", "timeout", "setTimeout", "clearTimeout", "container", "current", "handleScroll", "nearBottom", "scrollHeight", "scrollTop", "clientHeight", "addEventListener", "removeEventListener", "hasOpenFeedback", "some", "showFeedbackOptions", "scrollTo", "top", "behavior", "scrollIntoView", "getCurrentTime", "Date", "toLocaleTimeString", "hour", "minute", "hour12", "handleUserPrompt", "trim", "time", "userMessage", "generatingMsg", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleNewChat", "renderChatView", "Fragment", "ref", "idx", "key", "onClick", "getFullUserName", "placeholder", "value", "onChange", "e", "target", "onKeyDown", "shift<PERSON>ey", "preventDefault", "disabled", "placement", "visible", "onClose", "headerStyle", "display", "title", "size"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/WIFY/AiChatBot/index.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport './aichatbot.css';\r\nimport { <PERSON><PERSON>, Drawer, Tooltip } from 'antd';\r\nimport { FiSend } from 'react-icons/fi';\r\nimport { SlArrowDownCircle } from 'react-icons/sl';\r\nimport { PiNotePencil } from 'react-icons/pi';\r\nimport { SlClose } from 'react-icons/sl';\r\nimport ConfigHelpers from '../../../util/ConfigHelpers';\r\nimport { isMobileView } from '../../../util/helpers';\r\nimport AgentMessage from './AgentMessage';\r\nimport UserMessage from './UserMessage';\r\nimport http_utils from '../../../util/http_utils';\r\nimport axios from 'axios';\r\n\r\nconst AiChatBot = ({ showAiChatBot, setShowAiChatBot }) => {\r\n    const [messages, setMessages] = useState([]);\r\n    const [input, setInput] = useState('');\r\n    const [sessionId, setSessionId] = useState('');\r\n    const [isScrolledUp, setIsScrolledUp] = useState(false);\r\n    const [helpfulMap, setHelpfulMap] = useState({});\r\n\r\n    // Refs for scroll handling\r\n    const lastMsgInChat = useRef(null);\r\n    const chatContainerRef = useRef(null);\r\n\r\n    function initViewData(latestMessage) {\r\n        console.log(\r\n            'AiChatBot :: initViewData :: latestMessage',\r\n            latestMessage\r\n        );\r\n\r\n        const newChat = messages?.length == 0;\r\n        var params = {\r\n            latestMessage,\r\n            is_new_chat: newChat,\r\n            session_id: sessionId,\r\n        };\r\n        const onComplete = (resp) => {\r\n            console.log(\r\n                'AiChatBot :: initViewData :: onComplete :: resp data',\r\n                resp.data\r\n            );\r\n            setMessages((prev) =>\r\n                prev.map((msg) =>\r\n                    msg.isGenerating\r\n                        ? {\r\n                              ...msg,\r\n                              text: resp?.data?.text,\r\n                              isGenerating: false,\r\n                              block: resp?.data?.message,\r\n                          }\r\n                        : msg\r\n                )\r\n            );\r\n            setSessionId(resp?.data?.session_id || '');\r\n            scrollToBottom();\r\n        };\r\n        const onError = (error) => {\r\n            console.log('AiChatBot :: error ::', error);\r\n        };\r\n        http_utils.performGetCall('/ai-chatbot/', params, onComplete, onError);\r\n    }\r\n    // Auto-scroll on user message\r\n    useEffect(() => {\r\n        const lastMessage = messages[messages.length - 1];\r\n        if (lastMessage?.from === 'user' || lastMessage?.isGenerating) {\r\n            const timeout = setTimeout(() => {\r\n                scrollToBottom();\r\n            }, 50);\r\n            return () => clearTimeout(timeout);\r\n        }\r\n    }, [messages]);\r\n\r\n    // Detect scroll position\r\n    useEffect(() => {\r\n        const container = chatContainerRef.current;\r\n        const handleScroll = () => {\r\n            const nearBottom =\r\n                container.scrollHeight - container.scrollTop <=\r\n                container.clientHeight + 100;\r\n            setIsScrolledUp(!nearBottom);\r\n        };\r\n\r\n        if (container) {\r\n            container.addEventListener('scroll', handleScroll);\r\n        }\r\n        // Check scroll position right after new message is added\r\n        if (container) {\r\n            const nearBottom =\r\n                container.scrollHeight - container.scrollTop <=\r\n                container.clientHeight + 100;\r\n            setIsScrolledUp(!nearBottom);\r\n        }\r\n\r\n        return () => {\r\n            if (container) {\r\n                container.removeEventListener('scroll', handleScroll);\r\n            }\r\n        };\r\n    }, [messages]);\r\n\r\n    // Scroll to bottom of message list\r\n    const scrollToBottom = () => {\r\n        if (chatContainerRef.current) {\r\n            // Check if any feedback section is currently open\r\n            const hasOpenFeedback = messages.some(\r\n                (msg) => msg.showFeedbackOptions\r\n            );\r\n\r\n            if (hasOpenFeedback) {\r\n                // If feedback section is open, scroll to the very bottom of the container\r\n                // to ensure the feedback section is visible\r\n                chatContainerRef.current.scrollTo({\r\n                    top: chatContainerRef.current.scrollHeight,\r\n                    behavior: 'smooth',\r\n                });\r\n            } else if (lastMsgInChat.current) {\r\n                // Normal scroll behavior when no feedback section is open\r\n                lastMsgInChat.current.scrollIntoView({\r\n                    behavior: 'smooth',\r\n                });\r\n            }\r\n        }\r\n    };\r\n\r\n    // Get formatted current time\r\n    const getCurrentTime = () =>\r\n        new Date().toLocaleTimeString([], {\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            hour12: true,\r\n        });\r\n\r\n    // Handle message send\r\n    const handleUserPrompt = () => {\r\n        console.log('AiChatBot :: handleUserPrompt', input);\r\n        if (!input.trim()) return;\r\n\r\n        const time = getCurrentTime();\r\n\r\n        const userMessage = {\r\n            from: 'user',\r\n            text: input,\r\n            time,\r\n        };\r\n\r\n        // Add the user message to the chat immediately\r\n        setMessages((prev) => [...prev, userMessage]);\r\n        setInput('');\r\n        // Show generating resp animation in chat\r\n        const generatingMsg = {\r\n            from: 'tms-ai',\r\n            text: <div className=\"wy-acb-initial-loading\"></div>,\r\n            time,\r\n            isGenerating: true,\r\n        };\r\n        setMessages((prev) => [...prev, generatingMsg]);\r\n\r\n        initViewData(userMessage);\r\n    };\r\n\r\n    const handleNewChat = () => {\r\n        setMessages([]);\r\n        setSessionId('');\r\n    };\r\n\r\n    const renderChatView = () => (\r\n        <>\r\n            <div className=\"wy-acb-messages\" ref={chatContainerRef}>\r\n                {messages.map((msg, idx) => {\r\n                    if (msg.from === 'user') {\r\n                        return <UserMessage key={idx} message={msg} />;\r\n                    }\r\n                    return (\r\n                        <AgentMessage\r\n                            key={idx}\r\n                            message={msg}\r\n                            isGenerating={msg.isGenerating}\r\n                            idx={idx}\r\n                            helpfulMap={helpfulMap}\r\n                            setHelpfulMap={setHelpfulMap}\r\n                            messages={messages}\r\n                            setMessages={setMessages}\r\n                            sessionId={sessionId}\r\n                        />\r\n                    );\r\n                })}\r\n                <div ref={lastMsgInChat} />\r\n            </div>\r\n\r\n            {isScrolledUp && (\r\n                <div>\r\n                    <button\r\n                        onClick={scrollToBottom}\r\n                        className=\"wy-acb-jump-to-latest-button\"\r\n                    >\r\n                        <SlArrowDownCircle /> Jump to Latest\r\n                    </button>\r\n                </div>\r\n            )}\r\n\r\n            {messages.length === 0 && (\r\n                <div className=\"wy-acb-intro-message gx-text-center\">\r\n                    <div className=\"wy-acb-name\">\r\n                        Hello! {ConfigHelpers.getFullUserName()}\r\n                    </div>{' '}\r\n                    <div className=\"wy-acb-intro-message-text\">\r\n                        I am your TMS AI Assistant.\r\n                        <br />\r\n                        How can I help you with your TMS operations today?\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            <div className=\"wy-acb-prompt-wrapper\">\r\n                <textarea\r\n                    placeholder=\"Type a message...\"\r\n                    value={input}\r\n                    //disable text area if isGenerating is true\r\n\r\n                    onChange={(e) => setInput(e.target.value)}\r\n                    onKeyDown={(e) => {\r\n                        const isGenerating = messages.some(\r\n                            (msg) => msg.isGenerating\r\n                        );\r\n                        if (e.key === 'Enter' && !e.shiftKey) {\r\n                            if (isGenerating) {\r\n                                e.preventDefault(); // Block submit while generating\r\n                            } else {\r\n                                e.preventDefault(); // ✅ Prevents new line\r\n                                handleUserPrompt();\r\n                            }\r\n                        }\r\n                    }}\r\n                    className=\"wy-acb-prompt-textarea\"\r\n                />\r\n                <button\r\n                    onClick={handleUserPrompt}\r\n                    className=\"wy-acb-send-button\"\r\n                    disabled={messages.some((msg) => msg.isGenerating)}\r\n                >\r\n                    <FiSend />\r\n                </button>\r\n            </div>\r\n        </>\r\n    );\r\n\r\n    return (\r\n        <>\r\n            <Drawer\r\n                placement={isMobileView() ? 'bottom' : 'right'}\r\n                visible={showAiChatBot}\r\n                onClose={() => setShowAiChatBot(false)}\r\n                className=\"wy-acb-drawer-wrapper\"\r\n                headerStyle={{ display: 'none' }}\r\n            >\r\n                {/* Main container */}\r\n                <div className=\"wy-acb-chat-container box\">\r\n                    {/* Header with controls */}\r\n                    <div className=\"wy-acb-chat-header gx-d-flex gx-justify-content-between gx-align-items-center\">\r\n                        <h2 className=\"gx-mb-0 wy-acb-title-ai-assistant\">\r\n                            Jiffy\r\n                        </h2>\r\n                        <div className=\"gx-d-flex gx-align-items-center\">\r\n                            <Tooltip title=\"Start New Chat\" placement=\"bottom\">\r\n                                <Button\r\n                                    size=\"small\"\r\n                                    onClick={handleNewChat}\r\n                                    className=\"gx-mr-0 gx-mb-0 wy-acb-new-chat-button gx-fs-18 gx-d-flex gx-align-items-center\"\r\n                                >\r\n                                    <PiNotePencil />\r\n                                </Button>\r\n                            </Tooltip>\r\n                            {/* <button\r\n                                onClick={() => setShowAiChatBot(false)}\r\n                                className=\"gx-bg-transparent gx-border-0 gx-text-red gx-fs-md wy-cursor-pointer main-interface-close-button gx-d-none\"\r\n                            >\r\n                                <SlClose />\r\n                            </button> */}\r\n                        </div>\r\n                    </div>\r\n                    {renderChatView()}\r\n                </div>\r\n            </Drawer>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default AiChatBot;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,iBAAiB;AACxB,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AAC9C,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,OAAO,QAAQ,gBAAgB;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,KAAK,MAAM,OAAO;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAiB,CAAC,KAAK;EACvD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACA,MAAM6B,aAAa,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM6B,gBAAgB,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAErC,SAAS8B,YAAYA,CAACC,aAAa,EAAE;IACjCC,OAAO,CAACC,GAAG,CACP,4CAA4C,EAC5CF,aACJ,CAAC;IAED,MAAMG,OAAO,GAAG,CAAAhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,MAAM,KAAI,CAAC;IACrC,IAAIC,MAAM,GAAG;MACTL,aAAa;MACbM,WAAW,EAAEH,OAAO;MACpBI,UAAU,EAAEhB;IAChB,CAAC;IACD,MAAMiB,UAAU,GAAIC,IAAI,IAAK;MAAA,IAAAC,WAAA;MACzBT,OAAO,CAACC,GAAG,CACP,sDAAsD,EACtDO,IAAI,CAACE,IACT,CAAC;MACDvB,WAAW,CAAEwB,IAAI,IACbA,IAAI,CAACC,GAAG,CAAEC,GAAG;QAAA,IAAAC,UAAA,EAAAC,WAAA;QAAA,OACTF,GAAG,CAACG,YAAY,GACV;UACI,GAAGH,GAAG;UACNI,IAAI,EAAET,IAAI,aAAJA,IAAI,wBAAAM,UAAA,GAAJN,IAAI,CAAEE,IAAI,cAAAI,UAAA,uBAAVA,UAAA,CAAYG,IAAI;UACtBD,YAAY,EAAE,KAAK;UACnBE,KAAK,EAAEV,IAAI,aAAJA,IAAI,wBAAAO,WAAA,GAAJP,IAAI,CAAEE,IAAI,cAAAK,WAAA,uBAAVA,WAAA,CAAYI;QACvB,CAAC,GACDN,GAAG;MAAA,CACb,CACJ,CAAC;MACDtB,YAAY,CAAC,CAAAiB,IAAI,aAAJA,IAAI,wBAAAC,WAAA,GAAJD,IAAI,CAAEE,IAAI,cAAAD,WAAA,uBAAVA,WAAA,CAAYH,UAAU,KAAI,EAAE,CAAC;MAC1Cc,cAAc,CAAC,CAAC;IACpB,CAAC;IACD,MAAMC,OAAO,GAAIC,KAAK,IAAK;MACvBtB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEqB,KAAK,CAAC;IAC/C,CAAC;IACDzC,UAAU,CAAC0C,cAAc,CAAC,cAAc,EAAEnB,MAAM,EAAEG,UAAU,EAAEc,OAAO,CAAC;EAC1E;EACA;EACApD,SAAS,CAAC,MAAM;IACZ,MAAMuD,WAAW,GAAGtC,QAAQ,CAACA,QAAQ,CAACiB,MAAM,GAAG,CAAC,CAAC;IACjD,IAAI,CAAAqB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,IAAI,MAAK,MAAM,KAAID,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAER,YAAY,GAAE;MAC3D,MAAMU,OAAO,GAAGC,UAAU,CAAC,MAAM;QAC7BP,cAAc,CAAC,CAAC;MACpB,CAAC,EAAE,EAAE,CAAC;MACN,OAAO,MAAMQ,YAAY,CAACF,OAAO,CAAC;IACtC;EACJ,CAAC,EAAE,CAACxC,QAAQ,CAAC,CAAC;;EAEd;EACAjB,SAAS,CAAC,MAAM;IACZ,MAAM4D,SAAS,GAAGhC,gBAAgB,CAACiC,OAAO;IAC1C,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACvB,MAAMC,UAAU,GACZH,SAAS,CAACI,YAAY,GAAGJ,SAAS,CAACK,SAAS,IAC5CL,SAAS,CAACM,YAAY,GAAG,GAAG;MAChC1C,eAAe,CAAC,CAACuC,UAAU,CAAC;IAChC,CAAC;IAED,IAAIH,SAAS,EAAE;MACXA,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IACtD;IACA;IACA,IAAIF,SAAS,EAAE;MACX,MAAMG,UAAU,GACZH,SAAS,CAACI,YAAY,GAAGJ,SAAS,CAACK,SAAS,IAC5CL,SAAS,CAACM,YAAY,GAAG,GAAG;MAChC1C,eAAe,CAAC,CAACuC,UAAU,CAAC;IAChC;IAEA,OAAO,MAAM;MACT,IAAIH,SAAS,EAAE;QACXA,SAAS,CAACQ,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;MACzD;IACJ,CAAC;EACL,CAAC,EAAE,CAAC7C,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMkC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIvB,gBAAgB,CAACiC,OAAO,EAAE;MAC1B;MACA,MAAMQ,eAAe,GAAGpD,QAAQ,CAACqD,IAAI,CAChC1B,GAAG,IAAKA,GAAG,CAAC2B,mBACjB,CAAC;MAED,IAAIF,eAAe,EAAE;QACjB;QACA;QACAzC,gBAAgB,CAACiC,OAAO,CAACW,QAAQ,CAAC;UAC9BC,GAAG,EAAE7C,gBAAgB,CAACiC,OAAO,CAACG,YAAY;UAC1CU,QAAQ,EAAE;QACd,CAAC,CAAC;MACN,CAAC,MAAM,IAAI/C,aAAa,CAACkC,OAAO,EAAE;QAC9B;QACAlC,aAAa,CAACkC,OAAO,CAACc,cAAc,CAAC;UACjCD,QAAQ,EAAE;QACd,CAAC,CAAC;MACN;IACJ;EACJ,CAAC;;EAED;EACA,MAAME,cAAc,GAAGA,CAAA,KACnB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;IAC9BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE;EACZ,CAAC,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BnD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEb,KAAK,CAAC;IACnD,IAAI,CAACA,KAAK,CAACgE,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,IAAI,GAAGR,cAAc,CAAC,CAAC;IAE7B,MAAMS,WAAW,GAAG;MAChB7B,IAAI,EAAE,MAAM;MACZR,IAAI,EAAE7B,KAAK;MACXiE;IACJ,CAAC;;IAED;IACAlE,WAAW,CAAEwB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE2C,WAAW,CAAC,CAAC;IAC7CjE,QAAQ,CAAC,EAAE,CAAC;IACZ;IACA,MAAMkE,aAAa,GAAG;MAClB9B,IAAI,EAAE,QAAQ;MACdR,IAAI,eAAEnD,KAAA,CAAA0F,aAAA;QAAKC,SAAS,EAAC,wBAAwB;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAM,CAAC;MACpDV,IAAI;MACJrC,YAAY,EAAE;IAClB,CAAC;IACD7B,WAAW,CAAEwB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE4C,aAAa,CAAC,CAAC;IAE/CzD,YAAY,CAACwD,WAAW,CAAC;EAC7B,CAAC;EAED,MAAMU,aAAa,GAAGA,CAAA,KAAM;IACxB7E,WAAW,CAAC,EAAE,CAAC;IACfI,YAAY,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAM0E,cAAc,GAAGA,CAAA,kBACnBnG,KAAA,CAAA0F,aAAA,CAAA1F,KAAA,CAAAoG,QAAA,qBACIpG,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAACU,GAAG,EAAEtE,gBAAiB;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClD7E,QAAQ,CAAC0B,GAAG,CAAC,CAACC,GAAG,EAAEuD,GAAG,KAAK;IACxB,IAAIvD,GAAG,CAACY,IAAI,KAAK,MAAM,EAAE;MACrB,oBAAO3D,KAAA,CAAA0F,aAAA,CAAC5E,WAAW;QAACyF,GAAG,EAAED,GAAI;QAACjD,OAAO,EAAEN,GAAI;QAAA6C,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAE,CAAC;IAClD;IACA,oBACIjG,KAAA,CAAA0F,aAAA,CAAC7E,YAAY;MACT0F,GAAG,EAAED,GAAI;MACTjD,OAAO,EAAEN,GAAI;MACbG,YAAY,EAAEH,GAAG,CAACG,YAAa;MAC/BoD,GAAG,EAAEA,GAAI;MACT1E,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7BT,QAAQ,EAAEA,QAAS;MACnBC,WAAW,EAAEA,WAAY;MACzBG,SAAS,EAAEA,SAAU;MAAAoE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACxB,CAAC;EAEV,CAAC,CAAC,eACFjG,KAAA,CAAA0F,aAAA;IAAKW,GAAG,EAAEvE,aAAc;IAAA8D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACzB,CAAC,EAELvE,YAAY,iBACT1B,KAAA,CAAA0F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIjG,KAAA,CAAA0F,aAAA;IACIc,OAAO,EAAElD,cAAe;IACxBqC,SAAS,EAAC,8BAA8B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExCjG,KAAA,CAAA0F,aAAA,CAAClF,iBAAiB;IAAAoF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,mBACjB,CACP,CACR,EAEA7E,QAAQ,CAACiB,MAAM,KAAK,CAAC,iBAClBrC,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,qCAAqC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChDjG,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAClB,EAACtF,aAAa,CAAC8F,eAAe,CAAC,CACrC,CAAC,EAAC,GAAG,eACVzG,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6BAEvC,eAAAjG,KAAA,CAAA0F,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,sDAEL,CACJ,CACR,eAEDjG,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCjG,KAAA,CAAA0F,aAAA;IACIgB,WAAW,EAAC,mBAAmB;IAC/BC,KAAK,EAAErF;IACP;IAAA;;IAEAsF,QAAQ,EAAGC,CAAC,IAAKtF,QAAQ,CAACsF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IAC1CI,SAAS,EAAGF,CAAC,IAAK;MACd,MAAM3D,YAAY,GAAG9B,QAAQ,CAACqD,IAAI,CAC7B1B,GAAG,IAAKA,GAAG,CAACG,YACjB,CAAC;MACD,IAAI2D,CAAC,CAACN,GAAG,KAAK,OAAO,IAAI,CAACM,CAAC,CAACG,QAAQ,EAAE;QAClC,IAAI9D,YAAY,EAAE;UACd2D,CAAC,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,MAAM;UACHJ,CAAC,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC;UACpB5B,gBAAgB,CAAC,CAAC;QACtB;MACJ;IACJ,CAAE;IACFM,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACrC,CAAC,eACFjG,KAAA,CAAA0F,aAAA;IACIc,OAAO,EAAEnB,gBAAiB;IAC1BM,SAAS,EAAC,oBAAoB;IAC9BuB,QAAQ,EAAE9F,QAAQ,CAACqD,IAAI,CAAE1B,GAAG,IAAKA,GAAG,CAACG,YAAY,CAAE;IAAA0C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEnDjG,KAAA,CAAA0F,aAAA,CAACnF,MAAM;IAAAqF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACL,CACP,CACP,CACL;EAED,oBACIjG,KAAA,CAAA0F,aAAA,CAAA1F,KAAA,CAAAoG,QAAA,qBACIpG,KAAA,CAAA0F,aAAA,CAACrF,MAAM;IACH8G,SAAS,EAAEvG,YAAY,CAAC,CAAC,GAAG,QAAQ,GAAG,OAAQ;IAC/CwG,OAAO,EAAElG,aAAc;IACvBmG,OAAO,EAAEA,CAAA,KAAMlG,gBAAgB,CAAC,KAAK,CAAE;IACvCwE,SAAS,EAAC,uBAAuB;IACjC2B,WAAW,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAGjCjG,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEtCjG,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,+EAA+E;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1FjG,KAAA,CAAA0F,aAAA;IAAIC,SAAS,EAAC,mCAAmC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAE9C,CAAC,eACLjG,KAAA,CAAA0F,aAAA;IAAKC,SAAS,EAAC,iCAAiC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5CjG,KAAA,CAAA0F,aAAA,CAACpF,OAAO;IAACkH,KAAK,EAAC,gBAAgB;IAACL,SAAS,EAAC,QAAQ;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9CjG,KAAA,CAAA0F,aAAA,CAACtF,MAAM;IACHqH,IAAI,EAAC,OAAO;IACZjB,OAAO,EAAEN,aAAc;IACvBP,SAAS,EAAC,iFAAiF;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3FjG,KAAA,CAAA0F,aAAA,CAACjF,YAAY;IAAAmF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACX,CACH,CAOR,CACJ,CAAC,EACLE,cAAc,CAAC,CACf,CACD,CACV,CAAC;AAEX,CAAC;AAED,eAAelF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}