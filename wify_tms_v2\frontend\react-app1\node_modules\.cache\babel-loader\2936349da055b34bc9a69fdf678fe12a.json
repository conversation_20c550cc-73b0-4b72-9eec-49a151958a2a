{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\WIFY\\\\AiChatBot\\\\AgentMessage.js\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport { GoThumbsup, GoThumbsdown, GoComment, GoSync } from 'react-icons/go';\nimport { Alert, Button, Input, message as notification, Tooltip } from 'antd';\nimport { MdOutlineClose } from 'react-icons/md';\nimport RenderAgentResponseBlock from './ReactMarkDown';\nimport http_utils from '../../../util/http_utils';\n\n// Feedback options configuration\nconst FEEDBACK_OPTIONS = [{\n  key: 'confused',\n  label: \"Didn't fully follow instructions\"\n}, {\n  key: 'irrelevant',\n  label: 'Inaccurate result'\n}, {\n  key: 'not_relevant_2',\n  label: 'This does not make sense'\n}];\n\n// FeedbackSection component moved outside to prevent re-creation on every render\nconst FeedbackSection = ({\n  idx,\n  msg,\n  toggleFeedbackSection,\n  handleFeedbackClick,\n  handleCustomFeedbackChange,\n  handleCustomFeedbackSubmit\n}) => {\n  const wrapperRef = useRef(null);\n  useEffect(() => {\n    function handleClickOutside(event) {\n      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {\n        toggleFeedbackSection(idx); // Close feedback section\n      }\n    }\n    document.addEventListener('click', handleClickOutside);\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, [idx, toggleFeedbackSection]);\n  return msg.showFeedbackOptions && /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: \"wy-acb-feedback-section-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-section-header gx-mb-3 gx-position-relative\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-title-decor\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 21\n    }\n  }, \"Tell us more...\"), /*#__PURE__*/React.createElement(MdOutlineClose, {\n    className: \"gx-text-red wy-cursor-pointer gx-fs-xl\",\n    onClick: () => toggleFeedbackSection(idx),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 21\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 25\n    }\n  }, FEEDBACK_OPTIONS.map(option => {\n    var _msg$feedbackType;\n    return /*#__PURE__*/React.createElement(Button, {\n      key: option.key,\n      block: true,\n      type: ((_msg$feedbackType = msg.feedbackType) === null || _msg$feedbackType === void 0 ? void 0 : _msg$feedbackType.includes(option.key)) ? 'primary' : 'ghost',\n      onClick: () => handleFeedbackClick(idx, option.key),\n      className: \"gx-mr-0\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 33\n      }\n    }, option.label);\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(Input.TextArea, {\n    value: msg.customFeedback || '',\n    placeholder: \"Please describe what could be improved... (optional)\",\n    rows: 2,\n    className: \"wy-acb-textarea\",\n    onChange: e => handleCustomFeedbackChange(idx, e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 29\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-text-right gx-mt-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    danger: true,\n    type: \"text\",\n    onClick: () => toggleFeedbackSection(idx),\n    className: \"gx-mr-2 gx-mt-1 gx-mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 29\n    }\n  }, \"Cancel\"), /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    onClick: () => handleCustomFeedbackSubmit(idx),\n    className: \"gx-mt-1 gx-mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 29\n    }\n  }, \"Submit\")))));\n};\nconst AgentMessage = ({\n  message,\n  isGenerating,\n  idx,\n  helpfulMap,\n  setHelpfulMap,\n  messages,\n  setMessages,\n  sessionId\n}) => {\n  // API call to save rating to database\n  const submitRatingToDatabase = async ratingData => {\n    try {\n      // console.log('Submitting rating to database:', ratingData);\n\n      const onComplete = resp => {\n        console.log('Rating submitted successfully:', resp);\n        // notification.success('Thank you for your feedback!');\n      };\n      const onError = error => {\n        console.error('Error submitting rating:', error);\n        notification.error('Failed to submit feedback. Please try again.');\n      };\n      http_utils.performPostCall('/ai-chatbot/va-chatbot-resp-rating', ratingData, onComplete, onError);\n    } catch (error) {\n      console.error('Error in submitRatingToDatabase:', error);\n      notification.error('Failed to submit feedback. Please try again.');\n    }\n  };\n\n  // Get user message that corresponds to this AI response\n  const getUserMessageForResponse = currentIdx => {\n    // Look for the previous user message\n    for (let i = currentIdx - 1; i >= 0; i--) {\n      var _messages$i;\n      if (((_messages$i = messages[i]) === null || _messages$i === void 0 ? void 0 : _messages$i.from) === 'user') {\n        return messages[i];\n      }\n    }\n    return null;\n  };\n\n  // Handle thumbs up helpful click\n  const handleThumbsUpHelpfulClick = idx => {\n    var _messages$idx;\n    // Check if already rated\n    if ((_messages$idx = messages[idx]) === null || _messages$idx === void 0 ? void 0 : _messages$idx.ratingSubmitted) {\n      return;\n    }\n    setHelpfulMap(prev => ({\n      ...prev,\n      [idx]: !prev[idx]\n    }));\n    setMessages(prev => prev.map((msg, i) => i === idx ? {\n      ...msg,\n      showFeedbackOptions: false,\n      ratingSubmitted: true,\n      ratingType: 'positive'\n    } : msg));\n\n    // Submit positive rating to database\n    const userMessage = getUserMessageForResponse(idx);\n    const currentMessage = messages[idx];\n    const ratingData = {\n      session_id: sessionId,\n      usr_prompt: (userMessage === null || userMessage === void 0 ? void 0 : userMessage.text) || '',\n      va_response: currentMessage,\n      good_rating: true,\n      bad_rating: false,\n      additional_comments: '',\n      sugg_feedback: ''\n    };\n    submitRatingToDatabase(ratingData);\n  };\n\n  // Toggle feedback section\n  const toggleFeedbackSection = idx => {\n    var _messages$idx2;\n    // Check if already rated\n    if ((_messages$idx2 = messages[idx]) === null || _messages$idx2 === void 0 ? void 0 : _messages$idx2.ratingSubmitted) {\n      return;\n    }\n    setHelpfulMap(prev => ({\n      ...prev,\n      [idx]: false\n    }));\n    setMessages(prev => prev.map((msg, i) => i === idx ? {\n      ...msg,\n      showFeedbackOptions: !msg.showFeedbackOptions\n    } : msg));\n  };\n\n  // Handle feedback type selection\n  const handleFeedbackClick = (idx, selectedType) => {\n    setMessages(prevMessages => prevMessages.map((msg, i) => {\n      if (i !== idx) return msg;\n      const currentTypes = Array.isArray(msg.feedbackType) ? msg.feedbackType : [];\n      const alreadySelected = currentTypes.includes(selectedType);\n      const updatedTypes = alreadySelected ? currentTypes.filter(type => type !== selectedType) // deselect\n      : [...currentTypes, selectedType]; // select\n\n      return {\n        ...msg,\n        feedbackType: updatedTypes,\n        showFeedbackOptions: true,\n        customFeedback: msg.customFeedback || ''\n      };\n    }));\n  };\n\n  // Handle custom feedback text change\n  const handleCustomFeedbackChange = (index, value) => {\n    setMessages(prevMessages => prevMessages.map((msg, i) => i === index ? {\n      ...msg,\n      customFeedback: value\n    } : msg));\n  };\n\n  // Handle feedback submission\n  const handleCustomFeedbackSubmit = index => {\n    const currentMessage = messages[index];\n    const userMessage = getUserMessageForResponse(index);\n\n    // Prepare detailed feedback data\n    const feedbackTypes = (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.feedbackType) || [];\n    const customFeedback = (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.customFeedback) || '';\n\n    // Map feedback types to readable format using the same array\n    const feedbackMapping = FEEDBACK_OPTIONS.reduce((acc, option) => {\n      acc[option.key] = option.label;\n      return acc;\n    }, {});\n    const suggFeedback = feedbackTypes.map(type => feedbackMapping[type] || type).join(', ');\n    const ratingData = {\n      session_id: sessionId,\n      usr_prompt: (userMessage === null || userMessage === void 0 ? void 0 : userMessage.text) || '',\n      va_response: {\n        text: (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.text) || '',\n        block: (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.block) || null,\n        time: (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.time) || ''\n      },\n      good_rating: false,\n      bad_rating: true,\n      additional_comments: customFeedback,\n      sugg_feedback: suggFeedback\n    };\n\n    // Submit to database\n    submitRatingToDatabase(ratingData);\n    setHelpfulMap(prev => ({\n      ...prev,\n      [index]: false\n    }));\n    setMessages(prev => prev.map((msg, i) => i === index ? {\n      ...msg,\n      showFeedbackOptions: false,\n      feedbackType: null,\n      customFeedback: '',\n      feedbackSubmitted: true,\n      ratingSubmitted: true,\n      ratingType: 'negative'\n    } : msg));\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-message-wrapper ai\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-message-bubble\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 17\n    }\n  }, isGenerating ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-initial-loading\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 25\n    }\n  }) // Loading animation\n  : /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxHeight: '350px',\n      overflowY: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(RenderAgentResponseBlock, {\n    text: message.text,\n    block: message === null || message === void 0 ? void 0 : message.block,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 29\n    }\n  }))), !message.isWaiting && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-d-flex gx-align-items-center gx-justify-content-between gx-mt-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-bubble-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-bubble\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: `wy-acb-feedback-btn helpful ${helpfulMap[idx] || message.ratingType === 'positive' ? 'wy-acb-feedback-helpful-btn-active' : ''} ${message.showFeedbackOptions || message.feedbackSubmitted ? 'gx-d-none' : ''} ${message.ratingSubmitted ? 'disabled' : ''}`,\n    title: message.ratingSubmitted ? 'Already rated' : 'Helpful',\n    disabled: message.ratingSubmitted,\n    onClick: () => handleThumbsUpHelpfulClick(idx),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Tooltip, {\n    placement: \"bottom\",\n    title: \"Helpful\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(GoThumbsup, {\n    stroke: helpfulMap[idx] || message.ratingType === 'positive' ? 'green' : undefined,\n    strokeWidth: helpfulMap[idx] || message.ratingType === 'positive' ? 1 : 0,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 41\n    }\n  }))), /*#__PURE__*/React.createElement(\"button\", {\n    className: `wy-acb-feedback-btn not-helpful\n                                                                            ${message.feedbackSubmitted || message.showFeedbackOptions || message.ratingType === 'negative' ? 'wy-acb-feedback-not-helpful-btn-active' : ''}\n                                                                            ${helpfulMap[idx] ? 'gx-d-none' : ''} ${message.ratingSubmitted ? 'disabled' : ''}`,\n    title: message.ratingSubmitted ? 'Already rated' : 'Not Helpful',\n    disabled: message.ratingSubmitted,\n    onClick: () => toggleFeedbackSection(idx),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Tooltip, {\n    placement: \"bottom\",\n    title: \"Not Helpful\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(GoThumbsdown, {\n    color: message.showFeedbackOptions || message.ratingType === 'negative' ? 'black' : undefined,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 41\n    }\n  })))), message.feedbackSubmitted && /*#__PURE__*/React.createElement(Alert, {\n    showIcon: true,\n    message: \"Thank you for your feedback!\",\n    type: \"success\",\n    className: \"gx-mt-2 wy-acb-full-width-chat-area gx-fs-sm\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-timestamp gx-d-none\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 25\n    }\n  }, message.time)), message.showFeedbackOptions && /*#__PURE__*/React.createElement(FeedbackSection, {\n    idx: idx,\n    msg: message,\n    toggleFeedbackSection: toggleFeedbackSection,\n    handleFeedbackClick: handleFeedbackClick,\n    handleCustomFeedbackChange: handleCustomFeedbackChange,\n    handleCustomFeedbackSubmit: handleCustomFeedbackSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 21\n    }\n  })));\n};\nexport default AgentMessage;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "GoThumbsup", "GoThumbsdown", "GoComment", "GoSync", "<PERSON><PERSON>", "<PERSON><PERSON>", "Input", "message", "notification", "<PERSON><PERSON><PERSON>", "MdOutlineClose", "RenderAgentResponseBlock", "http_utils", "FEEDBACK_OPTIONS", "key", "label", "FeedbackSection", "idx", "msg", "toggleFeedbackSection", "handleFeedbackClick", "handleCustomFeedbackChange", "handleCustomFeedbackSubmit", "wrapperRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "showFeedbackOptions", "createElement", "ref", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "option", "_msg$feedbackType", "block", "type", "feedbackType", "includes", "TextArea", "value", "customFeedback", "placeholder", "rows", "onChange", "e", "danger", "AgentMessage", "isGenerating", "helpfulMap", "setHelpfulMap", "messages", "setMessages", "sessionId", "submitRatingToDatabase", "ratingData", "onComplete", "resp", "console", "log", "onError", "error", "performPostCall", "getUserMessageForResponse", "currentIdx", "i", "_messages$i", "from", "handleThumbsUpHelpfulClick", "_messages$idx", "ratingSubmitted", "prev", "ratingType", "userMessage", "currentMessage", "session_id", "usr_prompt", "text", "va_response", "good_rating", "bad_rating", "additional_comments", "sugg_feedback", "_messages$idx2", "selectedType", "prevMessages", "currentTypes", "Array", "isArray", "alreadySelected", "updatedTypes", "filter", "index", "feedbackTypes", "feedbackMapping", "reduce", "acc", "suggFeedback", "join", "time", "feedbackSubmitted", "style", "maxHeight", "overflowY", "isWaiting", "title", "disabled", "placement", "stroke", "undefined", "strokeWidth", "color", "showIcon"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/WIFY/AiChatBot/AgentMessage.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { GoThumbsup, GoThumbsdown, GoComment, GoSync } from 'react-icons/go';\r\nimport { Alert, Button, Input, message as notification, Tooltip } from 'antd';\r\nimport { MdOutlineClose } from 'react-icons/md';\r\nimport RenderAgentResponseBlock from './ReactMarkDown';\r\nimport http_utils from '../../../util/http_utils';\r\n\r\n// Feedback options configuration\r\nconst FEEDBACK_OPTIONS = [\r\n    {\r\n        key: 'confused',\r\n        label: \"Didn't fully follow instructions\",\r\n    },\r\n    {\r\n        key: 'irrelevant',\r\n        label: 'Inaccurate result',\r\n    },\r\n    {\r\n        key: 'not_relevant_2',\r\n        label: 'This does not make sense',\r\n    },\r\n];\r\n\r\n// FeedbackSection component moved outside to prevent re-creation on every render\r\nconst FeedbackSection = ({\r\n    idx,\r\n    msg,\r\n    toggleFeedbackSection,\r\n    handleFeedbackClick,\r\n    handleCustomFeedbackChange,\r\n    handleCustomFeedbackSubmit,\r\n}) => {\r\n    const wrapperRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        function handleClickOutside(event) {\r\n            if (\r\n                wrapperRef.current &&\r\n                !wrapperRef.current.contains(event.target)\r\n            ) {\r\n                toggleFeedbackSection(idx); // Close feedback section\r\n            }\r\n        }\r\n\r\n        document.addEventListener('click', handleClickOutside);\r\n        return () => {\r\n            document.removeEventListener('click', handleClickOutside);\r\n        };\r\n    }, [idx, toggleFeedbackSection]);\r\n\r\n    return (\r\n        msg.showFeedbackOptions && (\r\n            <div ref={wrapperRef} className=\"wy-acb-feedback-section-wrapper\">\r\n                <div className=\"wy-acb-feedback-section-header gx-mb-3 gx-position-relative\">\r\n                    <div className=\"wy-acb-title-decor\"></div>\r\n                    <div>Tell us more...</div>\r\n                    <MdOutlineClose\r\n                        className=\"gx-text-red wy-cursor-pointer gx-fs-xl\"\r\n                        onClick={() => toggleFeedbackSection(idx)}\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <div>\r\n                        <div className=\"wy-acb-feedback-section\">\r\n                            {FEEDBACK_OPTIONS.map((option) => (\r\n                                <Button\r\n                                    key={option.key}\r\n                                    block\r\n                                    type={\r\n                                        msg.feedbackType?.includes(option.key)\r\n                                            ? 'primary'\r\n                                            : 'ghost'\r\n                                    }\r\n                                    onClick={() =>\r\n                                        handleFeedbackClick(idx, option.key)\r\n                                    }\r\n                                    className=\"gx-mr-0\"\r\n                                >\r\n                                    {option.label}\r\n                                </Button>\r\n                            ))}\r\n                        </div>\r\n                        <div className=\"gx-mb-2\">\r\n                            <Input.TextArea\r\n                                value={msg.customFeedback || ''}\r\n                                placeholder=\"Please describe what could be improved... (optional)\"\r\n                                rows={2}\r\n                                className=\"wy-acb-textarea\"\r\n                                onChange={(e) =>\r\n                                    handleCustomFeedbackChange(\r\n                                        idx,\r\n                                        e.target.value\r\n                                    )\r\n                                }\r\n                            />\r\n                        </div>\r\n                        <div className=\"gx-text-right gx-mt-1\">\r\n                            <Button\r\n                                danger\r\n                                type=\"text\"\r\n                                onClick={() => toggleFeedbackSection(idx)}\r\n                                className=\"gx-mr-2 gx-mt-1 gx-mb-0\"\r\n                            >\r\n                                Cancel\r\n                            </Button>\r\n                            <Button\r\n                                type=\"primary\"\r\n                                onClick={() => handleCustomFeedbackSubmit(idx)}\r\n                                className=\"gx-mt-1 gx-mb-0\"\r\n                            >\r\n                                Submit\r\n                            </Button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        )\r\n    );\r\n};\r\n\r\nconst AgentMessage = ({\r\n    message,\r\n    isGenerating,\r\n    idx,\r\n    helpfulMap,\r\n    setHelpfulMap,\r\n    messages,\r\n    setMessages,\r\n    sessionId,\r\n}) => {\r\n    // API call to save rating to database\r\n    const submitRatingToDatabase = async (ratingData) => {\r\n        try {\r\n            // console.log('Submitting rating to database:', ratingData);\r\n\r\n            const onComplete = (resp) => {\r\n                console.log('Rating submitted successfully:', resp);\r\n                // notification.success('Thank you for your feedback!');\r\n            };\r\n\r\n            const onError = (error) => {\r\n                console.error('Error submitting rating:', error);\r\n                notification.error(\r\n                    'Failed to submit feedback. Please try again.'\r\n                );\r\n            };\r\n\r\n            http_utils.performPostCall(\r\n                '/ai-chatbot/va-chatbot-resp-rating',\r\n                ratingData,\r\n                onComplete,\r\n                onError\r\n            );\r\n        } catch (error) {\r\n            console.error('Error in submitRatingToDatabase:', error);\r\n            notification.error('Failed to submit feedback. Please try again.');\r\n        }\r\n    };\r\n\r\n    // Get user message that corresponds to this AI response\r\n    const getUserMessageForResponse = (currentIdx) => {\r\n        // Look for the previous user message\r\n        for (let i = currentIdx - 1; i >= 0; i--) {\r\n            if (messages[i]?.from === 'user') {\r\n                return messages[i];\r\n            }\r\n        }\r\n        return null;\r\n    };\r\n\r\n    // Handle thumbs up helpful click\r\n    const handleThumbsUpHelpfulClick = (idx) => {\r\n        // Check if already rated\r\n        if (messages[idx]?.ratingSubmitted) {\r\n            return;\r\n        }\r\n\r\n        setHelpfulMap((prev) => ({\r\n            ...prev,\r\n            [idx]: !prev[idx],\r\n        }));\r\n\r\n        setMessages((prev) =>\r\n            prev.map((msg, i) =>\r\n                i === idx\r\n                    ? {\r\n                          ...msg,\r\n                          showFeedbackOptions: false,\r\n                          ratingSubmitted: true,\r\n                          ratingType: 'positive',\r\n                      }\r\n                    : msg\r\n            )\r\n        );\r\n\r\n        // Submit positive rating to database\r\n        const userMessage = getUserMessageForResponse(idx);\r\n        const currentMessage = messages[idx];\r\n\r\n        const ratingData = {\r\n            session_id: sessionId,\r\n            usr_prompt: userMessage?.text || '',\r\n            va_response: currentMessage,\r\n            good_rating: true,\r\n            bad_rating: false,\r\n            additional_comments: '',\r\n            sugg_feedback: '',\r\n        };\r\n\r\n        submitRatingToDatabase(ratingData);\r\n    };\r\n\r\n    // Toggle feedback section\r\n    const toggleFeedbackSection = (idx) => {\r\n        // Check if already rated\r\n        if (messages[idx]?.ratingSubmitted) {\r\n            return;\r\n        }\r\n\r\n        setHelpfulMap((prev) => ({\r\n            ...prev,\r\n            [idx]: false,\r\n        }));\r\n\r\n        setMessages((prev) =>\r\n            prev.map((msg, i) =>\r\n                i === idx\r\n                    ? { ...msg, showFeedbackOptions: !msg.showFeedbackOptions }\r\n                    : msg\r\n            )\r\n        );\r\n    };\r\n\r\n    // Handle feedback type selection\r\n    const handleFeedbackClick = (idx, selectedType) => {\r\n        setMessages((prevMessages) =>\r\n            prevMessages.map((msg, i) => {\r\n                if (i !== idx) return msg;\r\n\r\n                const currentTypes = Array.isArray(msg.feedbackType)\r\n                    ? msg.feedbackType\r\n                    : [];\r\n                const alreadySelected = currentTypes.includes(selectedType);\r\n\r\n                const updatedTypes = alreadySelected\r\n                    ? currentTypes.filter((type) => type !== selectedType) // deselect\r\n                    : [...currentTypes, selectedType]; // select\r\n\r\n                return {\r\n                    ...msg,\r\n                    feedbackType: updatedTypes,\r\n                    showFeedbackOptions: true,\r\n                    customFeedback: msg.customFeedback || '',\r\n                };\r\n            })\r\n        );\r\n    };\r\n\r\n    // Handle custom feedback text change\r\n    const handleCustomFeedbackChange = (index, value) => {\r\n        setMessages((prevMessages) =>\r\n            prevMessages.map((msg, i) =>\r\n                i === index ? { ...msg, customFeedback: value } : msg\r\n            )\r\n        );\r\n    };\r\n\r\n    // Handle feedback submission\r\n    const handleCustomFeedbackSubmit = (index) => {\r\n        const currentMessage = messages[index];\r\n        const userMessage = getUserMessageForResponse(index);\r\n\r\n        // Prepare detailed feedback data\r\n        const feedbackTypes = currentMessage?.feedbackType || [];\r\n        const customFeedback = currentMessage?.customFeedback || '';\r\n\r\n        // Map feedback types to readable format using the same array\r\n        const feedbackMapping = FEEDBACK_OPTIONS.reduce((acc, option) => {\r\n            acc[option.key] = option.label;\r\n            return acc;\r\n        }, {});\r\n\r\n        const suggFeedback = feedbackTypes\r\n            .map((type) => feedbackMapping[type] || type)\r\n            .join(', ');\r\n\r\n        const ratingData = {\r\n            session_id: sessionId,\r\n            usr_prompt: userMessage?.text || '',\r\n            va_response: {\r\n                text: currentMessage?.text || '',\r\n                block: currentMessage?.block || null,\r\n                time: currentMessage?.time || '',\r\n            },\r\n            good_rating: false,\r\n            bad_rating: true,\r\n            additional_comments: customFeedback,\r\n            sugg_feedback: suggFeedback,\r\n        };\r\n\r\n        // Submit to database\r\n        submitRatingToDatabase(ratingData);\r\n\r\n        setHelpfulMap((prev) => ({\r\n            ...prev,\r\n            [index]: false,\r\n        }));\r\n\r\n        setMessages((prev) =>\r\n            prev.map((msg, i) =>\r\n                i === index\r\n                    ? {\r\n                          ...msg,\r\n                          showFeedbackOptions: false,\r\n                          feedbackType: null,\r\n                          customFeedback: '',\r\n                          feedbackSubmitted: true,\r\n                          ratingSubmitted: true,\r\n                          ratingType: 'negative',\r\n                      }\r\n                    : msg\r\n            )\r\n        );\r\n    };\r\n\r\n    return (\r\n        <div className=\"wy-acb-message-wrapper ai\">\r\n            <div className=\"message-wrapper\">\r\n                <div className=\"wy-acb-message-bubble\">\r\n                    {isGenerating ? (\r\n                        <div className=\"wy-acb-initial-loading\"></div> // Loading animation\r\n                    ) : (\r\n                        <div style={{ maxHeight: '350px', overflowY: 'auto' }}>\r\n                            <RenderAgentResponseBlock\r\n                                text={message.text}\r\n                                block={message?.block}\r\n                            />\r\n                        </div>\r\n                    )}\r\n                </div>\r\n\r\n                {!message.isWaiting && (\r\n                    <div className=\"gx-d-flex gx-align-items-center gx-justify-content-between gx-mt-1\">\r\n                        <div className=\"wy-acb-feedback-bubble-wrapper\">\r\n                            <div className=\"wy-acb-feedback-bubble\">\r\n                                <button\r\n                                    className={`wy-acb-feedback-btn helpful ${\r\n                                        helpfulMap[idx] ||\r\n                                        message.ratingType === 'positive'\r\n                                            ? 'wy-acb-feedback-helpful-btn-active'\r\n                                            : ''\r\n                                    } ${message.showFeedbackOptions || message.feedbackSubmitted ? 'gx-d-none' : ''} ${\r\n                                        message.ratingSubmitted\r\n                                            ? 'disabled'\r\n                                            : ''\r\n                                    }`}\r\n                                    title={\r\n                                        message.ratingSubmitted\r\n                                            ? 'Already rated'\r\n                                            : 'Helpful'\r\n                                    }\r\n                                    disabled={message.ratingSubmitted}\r\n                                    onClick={() =>\r\n                                        handleThumbsUpHelpfulClick(idx)\r\n                                    }\r\n                                >\r\n                                    <Tooltip placement=\"bottom\" title=\"Helpful\">\r\n                                        <GoThumbsup\r\n                                            stroke={\r\n                                                helpfulMap[idx] ||\r\n                                                message.ratingType ===\r\n                                                    'positive'\r\n                                                    ? 'green'\r\n                                                    : undefined\r\n                                            }\r\n                                            strokeWidth={\r\n                                                helpfulMap[idx] ||\r\n                                                message.ratingType ===\r\n                                                    'positive'\r\n                                                    ? 1\r\n                                                    : 0\r\n                                            }\r\n                                        />\r\n                                    </Tooltip>\r\n                                </button>\r\n\r\n                                <button\r\n                                    className={`wy-acb-feedback-btn not-helpful\r\n                                                                            ${message.feedbackSubmitted || message.showFeedbackOptions || message.ratingType === 'negative' ? 'wy-acb-feedback-not-helpful-btn-active' : ''}\r\n                                                                            ${helpfulMap[idx] ? 'gx-d-none' : ''} ${\r\n                                                                                message.ratingSubmitted\r\n                                                                                    ? 'disabled'\r\n                                                                                    : ''\r\n                                                                            }`}\r\n                                    title={\r\n                                        message.ratingSubmitted\r\n                                            ? 'Already rated'\r\n                                            : 'Not Helpful'\r\n                                    }\r\n                                    disabled={message.ratingSubmitted}\r\n                                    onClick={() => toggleFeedbackSection(idx)}\r\n                                >\r\n                                    <Tooltip placement=\"bottom\" title=\"Not Helpful\">\r\n                                        <GoThumbsdown\r\n                                            color={\r\n                                                message.showFeedbackOptions ||\r\n                                                message.ratingType ===\r\n                                                    'negative'\r\n                                                    ? 'black'\r\n                                                    : undefined\r\n                                            }\r\n                                        />\r\n                                    </Tooltip>\r\n                                </button>\r\n                            </div>\r\n                            {message.feedbackSubmitted && (\r\n                                <Alert\r\n                                    showIcon\r\n                                    message=\"Thank you for your feedback!\"\r\n                                    type=\"success\"\r\n                                    className=\"gx-mt-2 wy-acb-full-width-chat-area gx-fs-sm\"\r\n                                />\r\n                            )}\r\n                        </div>\r\n                        <div className=\"wy-acb-timestamp gx-d-none\">\r\n                            {message.time}\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {/* Feedback Selection Section */}\r\n                {message.showFeedbackOptions && (\r\n                    <FeedbackSection\r\n                        idx={idx}\r\n                        msg={message}\r\n                        toggleFeedbackSection={toggleFeedbackSection}\r\n                        handleFeedbackClick={handleFeedbackClick}\r\n                        handleCustomFeedbackChange={handleCustomFeedbackChange}\r\n                        handleCustomFeedbackSubmit={handleCustomFeedbackSubmit}\r\n                    />\r\n                )}\r\n\r\n                {/* Show feedback buttons when AI has completed response */}\r\n                {/* {!isGenerating && (\r\n                    <div className=\"wy-acb-feedback-bubble\">\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn helpful\"\r\n                            title=\"Helpful\"\r\n                        >\r\n                            <GoThumbsup />\r\n                        </button>\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn not-helpful\"\r\n                            title=\"Not Helpful\"\r\n                        >\r\n                            <GoThumbsdown />\r\n                        </button>\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn comment\"\r\n                            title=\"Comments\"\r\n                        >\r\n                            <GoComment />\r\n                        </button>\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn regenerate\"\r\n                            title=\"Regenerate\"\r\n                        >\r\n                            <GoSync />\r\n                        </button>\r\n                    </div>\r\n                )} */}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AgentMessage;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;AAC5E,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,IAAIC,YAAY,EAAEC,OAAO,QAAQ,MAAM;AAC7E,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAOC,wBAAwB,MAAM,iBAAiB;AACtD,OAAOC,UAAU,MAAM,0BAA0B;;AAEjD;AACA,MAAMC,gBAAgB,GAAG,CACrB;EACIC,GAAG,EAAE,UAAU;EACfC,KAAK,EAAE;AACX,CAAC,EACD;EACID,GAAG,EAAE,YAAY;EACjBC,KAAK,EAAE;AACX,CAAC,EACD;EACID,GAAG,EAAE,gBAAgB;EACrBC,KAAK,EAAE;AACX,CAAC,CACJ;;AAED;AACA,MAAMC,eAAe,GAAGA,CAAC;EACrBC,GAAG;EACHC,GAAG;EACHC,qBAAqB;EACrBC,mBAAmB;EACnBC,0BAA0B;EAC1BC;AACJ,CAAC,KAAK;EACF,MAAMC,UAAU,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAE/BC,SAAS,CAAC,MAAM;IACZ,SAASyB,kBAAkBA,CAACC,KAAK,EAAE;MAC/B,IACIF,UAAU,CAACG,OAAO,IAClB,CAACH,UAAU,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAC5C;QACET,qBAAqB,CAACF,GAAG,CAAC,CAAC,CAAC;MAChC;IACJ;IAEAY,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEN,kBAAkB,CAAC;IACtD,OAAO,MAAM;MACTK,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEP,kBAAkB,CAAC;IAC7D,CAAC;EACL,CAAC,EAAE,CAACP,GAAG,EAAEE,qBAAqB,CAAC,CAAC;EAEhC,OACID,GAAG,CAACc,mBAAmB,iBACnBpC,KAAA,CAAAqC,aAAA;IAAKC,GAAG,EAAEX,UAAW;IAACY,SAAS,EAAC,iCAAiC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7D7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,6DAA6D;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxE7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAAC,eAC1C7C,KAAA,CAAAqC,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,iBAAoB,CAAC,eAC1B7C,KAAA,CAAAqC,aAAA,CAACvB,cAAc;IACXyB,SAAS,EAAC,wCAAwC;IAClDO,OAAO,EAAEA,CAAA,KAAMvB,qBAAqB,CAACF,GAAG,CAAE;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC7C,CACA,CAAC,eACN7C,KAAA,CAAAqC,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI7C,KAAA,CAAAqC,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnC5B,gBAAgB,CAAC8B,GAAG,CAAEC,MAAM;IAAA,IAAAC,iBAAA;IAAA,oBACzBjD,KAAA,CAAAqC,aAAA,CAAC5B,MAAM;MACHS,GAAG,EAAE8B,MAAM,CAAC9B,GAAI;MAChBgC,KAAK;MACLC,IAAI,EACA,EAAAF,iBAAA,GAAA3B,GAAG,CAAC8B,YAAY,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBI,QAAQ,CAACL,MAAM,CAAC9B,GAAG,CAAC,IAChC,SAAS,GACT,OACT;MACD4B,OAAO,EAAEA,CAAA,KACLtB,mBAAmB,CAACH,GAAG,EAAE2B,MAAM,CAAC9B,GAAG,CACtC;MACDqB,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAElBG,MAAM,CAAC7B,KACJ,CAAC;EAAA,CACZ,CACA,CAAC,eACNnB,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB7C,KAAA,CAAAqC,aAAA,CAAC3B,KAAK,CAAC4C,QAAQ;IACXC,KAAK,EAAEjC,GAAG,CAACkC,cAAc,IAAI,EAAG;IAChCC,WAAW,EAAC,sDAAsD;IAClEC,IAAI,EAAE,CAAE;IACRnB,SAAS,EAAC,iBAAiB;IAC3BoB,QAAQ,EAAGC,CAAC,IACRnC,0BAA0B,CACtBJ,GAAG,EACHuC,CAAC,CAAC5B,MAAM,CAACuB,KACb,CACH;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACJ,CACA,CAAC,eACN7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC7C,KAAA,CAAAqC,aAAA,CAAC5B,MAAM;IACHoD,MAAM;IACNV,IAAI,EAAC,MAAM;IACXL,OAAO,EAAEA,CAAA,KAAMvB,qBAAqB,CAACF,GAAG,CAAE;IAC1CkB,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,QAEO,CAAC,eACT7C,KAAA,CAAAqC,aAAA,CAAC5B,MAAM;IACH0C,IAAI,EAAC,SAAS;IACdL,OAAO,EAAEA,CAAA,KAAMpB,0BAA0B,CAACL,GAAG,CAAE;IAC/CkB,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B,QAEO,CACP,CACJ,CACJ,CACJ,CACR;AAET,CAAC;AAED,MAAMiB,YAAY,GAAGA,CAAC;EAClBnD,OAAO;EACPoD,YAAY;EACZ1C,GAAG;EACH2C,UAAU;EACVC,aAAa;EACbC,QAAQ;EACRC,WAAW;EACXC;AACJ,CAAC,KAAK;EACF;EACA,MAAMC,sBAAsB,GAAG,MAAOC,UAAU,IAAK;IACjD,IAAI;MACA;;MAEA,MAAMC,UAAU,GAAIC,IAAI,IAAK;QACzBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,IAAI,CAAC;QACnD;MACJ,CAAC;MAED,MAAMG,OAAO,GAAIC,KAAK,IAAK;QACvBH,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDhE,YAAY,CAACgE,KAAK,CACd,8CACJ,CAAC;MACL,CAAC;MAED5D,UAAU,CAAC6D,eAAe,CACtB,oCAAoC,EACpCP,UAAU,EACVC,UAAU,EACVI,OACJ,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDhE,YAAY,CAACgE,KAAK,CAAC,8CAA8C,CAAC;IACtE;EACJ,CAAC;;EAED;EACA,MAAME,yBAAyB,GAAIC,UAAU,IAAK;IAC9C;IACA,KAAK,IAAIC,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAAA,IAAAC,WAAA;MACtC,IAAI,EAAAA,WAAA,GAAAf,QAAQ,CAACc,CAAC,CAAC,cAAAC,WAAA,uBAAXA,WAAA,CAAaC,IAAI,MAAK,MAAM,EAAE;QAC9B,OAAOhB,QAAQ,CAACc,CAAC,CAAC;MACtB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAMG,0BAA0B,GAAI9D,GAAG,IAAK;IAAA,IAAA+D,aAAA;IACxC;IACA,KAAAA,aAAA,GAAIlB,QAAQ,CAAC7C,GAAG,CAAC,cAAA+D,aAAA,uBAAbA,aAAA,CAAeC,eAAe,EAAE;MAChC;IACJ;IAEApB,aAAa,CAAEqB,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACjE,GAAG,GAAG,CAACiE,IAAI,CAACjE,GAAG;IACpB,CAAC,CAAC,CAAC;IAEH8C,WAAW,CAAEmB,IAAI,IACbA,IAAI,CAACvC,GAAG,CAAC,CAACzB,GAAG,EAAE0D,CAAC,KACZA,CAAC,KAAK3D,GAAG,GACH;MACI,GAAGC,GAAG;MACNc,mBAAmB,EAAE,KAAK;MAC1BiD,eAAe,EAAE,IAAI;MACrBE,UAAU,EAAE;IAChB,CAAC,GACDjE,GACV,CACJ,CAAC;;IAED;IACA,MAAMkE,WAAW,GAAGV,yBAAyB,CAACzD,GAAG,CAAC;IAClD,MAAMoE,cAAc,GAAGvB,QAAQ,CAAC7C,GAAG,CAAC;IAEpC,MAAMiD,UAAU,GAAG;MACfoB,UAAU,EAAEtB,SAAS;MACrBuB,UAAU,EAAE,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,KAAI,EAAE;MACnCC,WAAW,EAAEJ,cAAc;MAC3BK,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,KAAK;MACjBC,mBAAmB,EAAE,EAAE;MACvBC,aAAa,EAAE;IACnB,CAAC;IAED5B,sBAAsB,CAACC,UAAU,CAAC;EACtC,CAAC;;EAED;EACA,MAAM/C,qBAAqB,GAAIF,GAAG,IAAK;IAAA,IAAA6E,cAAA;IACnC;IACA,KAAAA,cAAA,GAAIhC,QAAQ,CAAC7C,GAAG,CAAC,cAAA6E,cAAA,uBAAbA,cAAA,CAAeb,eAAe,EAAE;MAChC;IACJ;IAEApB,aAAa,CAAEqB,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACjE,GAAG,GAAG;IACX,CAAC,CAAC,CAAC;IAEH8C,WAAW,CAAEmB,IAAI,IACbA,IAAI,CAACvC,GAAG,CAAC,CAACzB,GAAG,EAAE0D,CAAC,KACZA,CAAC,KAAK3D,GAAG,GACH;MAAE,GAAGC,GAAG;MAAEc,mBAAmB,EAAE,CAACd,GAAG,CAACc;IAAoB,CAAC,GACzDd,GACV,CACJ,CAAC;EACL,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAGA,CAACH,GAAG,EAAE8E,YAAY,KAAK;IAC/ChC,WAAW,CAAEiC,YAAY,IACrBA,YAAY,CAACrD,GAAG,CAAC,CAACzB,GAAG,EAAE0D,CAAC,KAAK;MACzB,IAAIA,CAAC,KAAK3D,GAAG,EAAE,OAAOC,GAAG;MAEzB,MAAM+E,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACjF,GAAG,CAAC8B,YAAY,CAAC,GAC9C9B,GAAG,CAAC8B,YAAY,GAChB,EAAE;MACR,MAAMoD,eAAe,GAAGH,YAAY,CAAChD,QAAQ,CAAC8C,YAAY,CAAC;MAE3D,MAAMM,YAAY,GAAGD,eAAe,GAC9BH,YAAY,CAACK,MAAM,CAAEvD,IAAI,IAAKA,IAAI,KAAKgD,YAAY,CAAC,CAAC;MAAA,EACrD,CAAC,GAAGE,YAAY,EAAEF,YAAY,CAAC,CAAC,CAAC;;MAEvC,OAAO;QACH,GAAG7E,GAAG;QACN8B,YAAY,EAAEqD,YAAY;QAC1BrE,mBAAmB,EAAE,IAAI;QACzBoB,cAAc,EAAElC,GAAG,CAACkC,cAAc,IAAI;MAC1C,CAAC;IACL,CAAC,CACL,CAAC;EACL,CAAC;;EAED;EACA,MAAM/B,0BAA0B,GAAGA,CAACkF,KAAK,EAAEpD,KAAK,KAAK;IACjDY,WAAW,CAAEiC,YAAY,IACrBA,YAAY,CAACrD,GAAG,CAAC,CAACzB,GAAG,EAAE0D,CAAC,KACpBA,CAAC,KAAK2B,KAAK,GAAG;MAAE,GAAGrF,GAAG;MAAEkC,cAAc,EAAED;IAAM,CAAC,GAAGjC,GACtD,CACJ,CAAC;EACL,CAAC;;EAED;EACA,MAAMI,0BAA0B,GAAIiF,KAAK,IAAK;IAC1C,MAAMlB,cAAc,GAAGvB,QAAQ,CAACyC,KAAK,CAAC;IACtC,MAAMnB,WAAW,GAAGV,yBAAyB,CAAC6B,KAAK,CAAC;;IAEpD;IACA,MAAMC,aAAa,GAAG,CAAAnB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAErC,YAAY,KAAI,EAAE;IACxD,MAAMI,cAAc,GAAG,CAAAiC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjC,cAAc,KAAI,EAAE;;IAE3D;IACA,MAAMqD,eAAe,GAAG5F,gBAAgB,CAAC6F,MAAM,CAAC,CAACC,GAAG,EAAE/D,MAAM,KAAK;MAC7D+D,GAAG,CAAC/D,MAAM,CAAC9B,GAAG,CAAC,GAAG8B,MAAM,CAAC7B,KAAK;MAC9B,OAAO4F,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,MAAMC,YAAY,GAAGJ,aAAa,CAC7B7D,GAAG,CAAEI,IAAI,IAAK0D,eAAe,CAAC1D,IAAI,CAAC,IAAIA,IAAI,CAAC,CAC5C8D,IAAI,CAAC,IAAI,CAAC;IAEf,MAAM3C,UAAU,GAAG;MACfoB,UAAU,EAAEtB,SAAS;MACrBuB,UAAU,EAAE,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,KAAI,EAAE;MACnCC,WAAW,EAAE;QACTD,IAAI,EAAE,CAAAH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,IAAI,KAAI,EAAE;QAChC1C,KAAK,EAAE,CAAAuC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEvC,KAAK,KAAI,IAAI;QACpCgE,IAAI,EAAE,CAAAzB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyB,IAAI,KAAI;MAClC,CAAC;MACDpB,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAExC,cAAc;MACnCyC,aAAa,EAAEe;IACnB,CAAC;;IAED;IACA3C,sBAAsB,CAACC,UAAU,CAAC;IAElCL,aAAa,CAAEqB,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACqB,KAAK,GAAG;IACb,CAAC,CAAC,CAAC;IAEHxC,WAAW,CAAEmB,IAAI,IACbA,IAAI,CAACvC,GAAG,CAAC,CAACzB,GAAG,EAAE0D,CAAC,KACZA,CAAC,KAAK2B,KAAK,GACL;MACI,GAAGrF,GAAG;MACNc,mBAAmB,EAAE,KAAK;MAC1BgB,YAAY,EAAE,IAAI;MAClBI,cAAc,EAAE,EAAE;MAClB2D,iBAAiB,EAAE,IAAI;MACvB9B,eAAe,EAAE,IAAI;MACrBE,UAAU,EAAE;IAChB,CAAC,GACDjE,GACV,CACJ,CAAC;EACL,CAAC;EAED,oBACItB,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtC7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCkB,YAAY,gBACT/D,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAAC,CAAC;EAAA,eAE/C7C,KAAA,CAAAqC,aAAA;IAAK+E,KAAK,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAA9E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClD7C,KAAA,CAAAqC,aAAA,CAACtB,wBAAwB;IACrB6E,IAAI,EAAEjF,OAAO,CAACiF,IAAK;IACnB1C,KAAK,EAAEvC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuC,KAAM;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzB,CACA,CAER,CAAC,EAEL,CAAClC,OAAO,CAAC4G,SAAS,iBACfvH,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,oEAAoE;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/E7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3C7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnC7C,KAAA,CAAAqC,aAAA;IACIE,SAAS,EAAE,+BACPyB,UAAU,CAAC3C,GAAG,CAAC,IACfV,OAAO,CAAC4E,UAAU,KAAK,UAAU,GAC3B,oCAAoC,GACpC,EAAE,IACR5E,OAAO,CAACyB,mBAAmB,IAAIzB,OAAO,CAACwG,iBAAiB,GAAG,WAAW,GAAG,EAAE,IAC3ExG,OAAO,CAAC0E,eAAe,GACjB,UAAU,GACV,EAAE,EACT;IACHmC,KAAK,EACD7G,OAAO,CAAC0E,eAAe,GACjB,eAAe,GACf,SACT;IACDoC,QAAQ,EAAE9G,OAAO,CAAC0E,eAAgB;IAClCvC,OAAO,EAAEA,CAAA,KACLqC,0BAA0B,CAAC9D,GAAG,CACjC;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAED7C,KAAA,CAAAqC,aAAA,CAACxB,OAAO;IAAC6G,SAAS,EAAC,QAAQ;IAACF,KAAK,EAAC,SAAS;IAAAhF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvC7C,KAAA,CAAAqC,aAAA,CAACjC,UAAU;IACPuH,MAAM,EACF3D,UAAU,CAAC3C,GAAG,CAAC,IACfV,OAAO,CAAC4E,UAAU,KACd,UAAU,GACR,OAAO,GACPqC,SACT;IACDC,WAAW,EACP7D,UAAU,CAAC3C,GAAG,CAAC,IACfV,OAAO,CAAC4E,UAAU,KACd,UAAU,GACR,CAAC,GACD,CACT;IAAA/C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACJ,CACI,CACL,CAAC,eAET7C,KAAA,CAAAqC,aAAA;IACIE,SAAS,EAAE;AAC/C,8EAA8E5B,OAAO,CAACwG,iBAAiB,IAAIxG,OAAO,CAACyB,mBAAmB,IAAIzB,OAAO,CAAC4E,UAAU,KAAK,UAAU,GAAG,wCAAwC,GAAG,EAAE;AAC3N,8EAA8EvB,UAAU,CAAC3C,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,IAChCV,OAAO,CAAC0E,eAAe,GACjB,UAAU,GACV,EAAE,EACT;IAC3CmC,KAAK,EACD7G,OAAO,CAAC0E,eAAe,GACjB,eAAe,GACf,aACT;IACDoC,QAAQ,EAAE9G,OAAO,CAAC0E,eAAgB;IAClCvC,OAAO,EAAEA,CAAA,KAAMvB,qBAAqB,CAACF,GAAG,CAAE;IAAAmB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1C7C,KAAA,CAAAqC,aAAA,CAACxB,OAAO;IAAC6G,SAAS,EAAC,QAAQ;IAACF,KAAK,EAAC,aAAa;IAAAhF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3C7C,KAAA,CAAAqC,aAAA,CAAChC,YAAY;IACTyH,KAAK,EACDnH,OAAO,CAACyB,mBAAmB,IAC3BzB,OAAO,CAAC4E,UAAU,KACd,UAAU,GACR,OAAO,GACPqC,SACT;IAAApF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACJ,CACI,CACL,CACP,CAAC,EACLlC,OAAO,CAACwG,iBAAiB,iBACtBnH,KAAA,CAAAqC,aAAA,CAAC7B,KAAK;IACFuH,QAAQ;IACRpH,OAAO,EAAC,8BAA8B;IACtCwC,IAAI,EAAC,SAAS;IACdZ,SAAS,EAAC,8CAA8C;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC3D,CAEJ,CAAC,eACN7C,KAAA,CAAAqC,aAAA;IAAKE,SAAS,EAAC,4BAA4B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtClC,OAAO,CAACuG,IACR,CACJ,CACR,EAGAvG,OAAO,CAACyB,mBAAmB,iBACxBpC,KAAA,CAAAqC,aAAA,CAACjB,eAAe;IACZC,GAAG,EAAEA,GAAI;IACTC,GAAG,EAAEX,OAAQ;IACbY,qBAAqB,EAAEA,qBAAsB;IAC7CC,mBAAmB,EAAEA,mBAAoB;IACzCC,0BAA0B,EAAEA,0BAA2B;IACvDC,0BAA0B,EAAEA,0BAA2B;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC1D,CAgCJ,CACJ,CAAC;AAEd,CAAC;AAED,eAAeiB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}