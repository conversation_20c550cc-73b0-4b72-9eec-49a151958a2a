const { getRandomInteger } = require('../../utils/helper');
const { allQueues } = require('../queues');
const { getUsersModelFrQueue } = require('./helpers/users_helper');

const performJob = async (job, done) => {
    const app = require('../../../app');
    const jobData = job.data;
    console.log('jobData', JSON.stringify(jobData));
    const { usr_id, dbResp, provided_current_status, provided_new_status } =
        jobData;
    const errorKeyVsLabel = {
        no_update_access: 'No update access',
        srvc_status_missing_fields:
            'Mandatory service status movement field missing',
        is_restricted: 'Restricted access',
    };
    let data = {};
    let result = {};
    let notification_title = '';
    if (dbResp?.status == 'ALL_UPDATED') {
        data['status'] = 'ALL_UPDATED';
        notification_title = `Successfully updated the status of ${dbResp.success_entries?.length} requests from ${provided_current_status} to ${provided_new_status} via Agent.`;
    } else if (dbResp?.status == 'PARTIALLY_UPDATED') {
        data['failed_entries'] = dbResp.failed_entries;
        data['status'] = 'PARTIALLY_UPDATED';
        dbResp.failed_entries.forEach(({ error, display_code }) => {
            if (!result[errorKeyVsLabel[error]]) {
                result[errorKeyVsLabel[error]] = [];
            }
            result[errorKeyVsLabel[error]].push({ display_code });
        });
        notification_title = `Partially updated the status of ${dbResp.success_entries?.length} requests from ${provided_current_status} to ${provided_new_status} via Agent.`;
    } else {
        data['failed_entries'] = dbResp.failed_entries;
        data['status'] = 'ALL_FAILED';
        dbResp.failed_entries.forEach(({ error, display_code }) => {
            if (!result[errorKeyVsLabel[error]]) {
                result[errorKeyVsLabel[error]] = [];
            }
            result[errorKeyVsLabel[error]].push({ display_code });
        });
        notification_title = `Unable to update the status of requests from ${provided_current_status} to ${provided_new_status} via Agent.`;
    }
    data['result'] = result;

    let notification_id = JSON.stringify(getRandomInteger(1, 999));
    try {
        const users_model = getUsersModelFrQueue(
            app,
            jobData.subtasks_model_data
        );
        console.log('usr_id', usr_id);
        const userLogins = await users_model.getUserLogins(usr_id);
        console.log('userLogins', userLogins);
        const userLoginsResp = JSON.parse(userLogins.resp);
        for (let single_logged_in_user_detail of userLoginsResp[0]
            .logged_in_user_details) {
            if (single_logged_in_user_detail.fcm_id) {
                console.log(
                    'single_logged_in_user_detail.fcm_id',
                    single_logged_in_user_detail.fcm_id
                );
                allQueues.WIFY_NOTIFICATION_SEND_FCM.addJob({
                    fcm_token: single_logged_in_user_detail.fcm_id,
                    title: notification_title,
                    message: '',
                    form_data: data,
                    notification_type: 'srvc_req_status_update_thr_va',
                    notificationContentType: 'taskAlert',
                    noti_id: notification_id,
                });
            }
        }
    } catch (error) {
        console.log('WIFY_NOTIFICATION_SEND_FCM', error);
    }
    try {
        allQueues.WIFY_TMS_ADD_NOTIFICATION_LOG.addJob({
            user_info: {
                org_id: jobData.org_id,
                usr_id: jobData.usr_id,
                ip_address: jobData.ip_address,
                user_agent: jobData.user_agent,
            },
            user_id: jobData.usr_id,
            module: 'subtask',
            module_id: 1,
            channel: 'fcm',
            notification_title: notification_title,
            notification_message: '',
            form_data: { ...jobData, ...data },
            notification_type: 'srvc_req_status_update_thr_va',
            notification_id,
        });
    } catch (error) {
        //swallow
        console.log('WIFY_TMS_ADD_NOTIFICATION_LOG', error);
    }

    done(null, {});
};
exports.default = performJob;
