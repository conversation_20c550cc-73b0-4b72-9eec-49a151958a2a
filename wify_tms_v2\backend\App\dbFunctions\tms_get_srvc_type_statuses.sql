CREATE OR REPLACE FUNCTION public.tms_get_srvc_type_statuses(org_id_ integer, usr_id_ uuid, org_name_ text, srvc_type_name_ text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	resp_data json;
	assgnd_org_ids integer[];
	
	begin
		
		-- 	Get assgnd org to user
		if usr_id_ is not null then
			assgnd_org_ids = tms_get_assgnd_org_to_user(usr_id_);
		end if;

		resp_data = array_to_json(array(select srvc_statuses.title 
			  from cl_cf_service_types as srvc_types
			 inner join cl_tx_orgs as orgs 
			    on orgs.nickname = org_name_
			 inner join cl_cf_srvc_statuses as srvc_statuses 
			    on srvc_statuses.srvc_id = srvc_types.service_type_id
			 where srvc_types.org_id = orgs.org_id	
			   and srvc_types.title = srvc_type_name_
			 group by orgs.org_id, srvc_types.service_type_id, srvc_statuses.db_id, srvc_statuses.title 
			 order by srvc_statuses.db_id 
		));
		return json_build_object('status',true,'code','success','data',resp_data);
	END;
$function$
;
