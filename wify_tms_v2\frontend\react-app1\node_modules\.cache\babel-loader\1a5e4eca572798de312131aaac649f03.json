{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\components\\\\WIFY\\\\AiChatBot\\\\AgentMessage.js\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport { GoThumbsup, GoThumbsdown, GoComment, GoSync } from 'react-icons/go';\nimport { Alert, Button, Input, message as notification, Tooltip } from 'antd';\nimport { MdOutlineClose } from 'react-icons/md';\nimport RenderAgentResponseBlock from './ReactMarkDown';\nimport http_utils from '../../../util/http_utils';\n\n// Feedback options configuration\nconst FEEDBACK_OPTIONS = [{\n  key: 'confused',\n  label: \"Didn't fully follow instructions\"\n}, {\n  key: 'irrelevant',\n  label: 'Inaccurate result'\n}, {\n  key: 'not_relevant_2',\n  label: 'This does not make sense'\n}];\n\n// FeedbackSection component moved outside to prevent re-creation on every render\nconst FeedbackSection = ({\n  idx,\n  msg,\n  toggleFeedbackSection,\n  handleFeedbackClick,\n  handleCustomFeedbackChange,\n  handleCustomFeedbackSubmit,\n  messages\n}) => {\n  const wrapperRef = useRef(null);\n  useEffect(() => {\n    function handleClickOutside(event) {\n      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {\n        toggleFeedbackSection(idx); // Close feedback section\n      }\n    }\n    document.addEventListener('click', handleClickOutside);\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, [idx, toggleFeedbackSection]);\n\n  // Scroll feedback section into view when it opens\n  useEffect(() => {\n    if (msg.showFeedbackOptions && wrapperRef.current) {\n      // Small delay to ensure the feedback section is fully rendered\n      setTimeout(() => {\n        const isLastMessage = idx === messages.length - 1;\n        if (isLastMessage) {\n          // If this is the last message, scroll to bottom of container\n          const chatContainer = wrapperRef.current.closest('.wy-acb-messages');\n          if (chatContainer) {\n            chatContainer.scrollTo({\n              top: chatContainer.scrollHeight,\n              behavior: 'smooth'\n            });\n          }\n        } else {\n          // For other messages, scroll feedback section into view\n          wrapperRef.current.scrollIntoView({\n            behavior: 'smooth',\n            block: 'nearest'\n          });\n        }\n      }, 100);\n    }\n  }, [msg.showFeedbackOptions, idx, messages.length]);\n  return msg.showFeedbackOptions && /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: \"wy-acb-feedback-section-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-section-header gx-mb-3 gx-position-relative\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-title-decor\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 21\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 21\n    }\n  }, \"Tell us more...\"), /*#__PURE__*/React.createElement(MdOutlineClose, {\n    className: \"gx-text-red wy-cursor-pointer gx-fs-xl\",\n    onClick: () => toggleFeedbackSection(idx),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 21\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 25\n    }\n  }, FEEDBACK_OPTIONS.map(option => {\n    var _msg$feedbackType;\n    return /*#__PURE__*/React.createElement(Button, {\n      key: option.key,\n      block: true,\n      type: ((_msg$feedbackType = msg.feedbackType) === null || _msg$feedbackType === void 0 ? void 0 : _msg$feedbackType.includes(option.key)) ? 'primary' : 'ghost',\n      onClick: () => handleFeedbackClick(idx, option.key),\n      className: \"gx-mr-0\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 33\n      }\n    }, option.label);\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-mb-2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(Input.TextArea, {\n    value: msg.customFeedback || '',\n    placeholder: \"Please describe what could be improved... (optional)\",\n    rows: 2,\n    className: \"wy-acb-textarea\",\n    onChange: e => handleCustomFeedbackChange(idx, e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 29\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-text-right gx-mt-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    danger: true,\n    type: \"text\",\n    onClick: () => toggleFeedbackSection(idx),\n    className: \"gx-mr-2 gx-mt-1 gx-mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 29\n    }\n  }, \"Cancel\"), /*#__PURE__*/React.createElement(Button, {\n    type: \"primary\",\n    onClick: () => handleCustomFeedbackSubmit(idx),\n    className: \"gx-mt-1 gx-mb-0\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 29\n    }\n  }, \"Submit\")))));\n};\nconst AgentMessage = ({\n  message,\n  isGenerating,\n  idx,\n  helpfulMap,\n  setHelpfulMap,\n  messages,\n  setMessages,\n  sessionId\n}) => {\n  // API call to save rating to database\n  const submitRatingToDatabase = async ratingData => {\n    try {\n      // console.log('Submitting rating to database:', ratingData);\n\n      const onComplete = resp => {\n        console.log('Rating submitted successfully:', resp);\n        // notification.success('Thank you for your feedback!');\n      };\n      const onError = error => {\n        console.error('Error submitting rating:', error);\n        notification.error('Failed to submit feedback. Please try again.');\n      };\n      http_utils.performPostCall('/ai-chatbot/va-chatbot-resp-rating', ratingData, onComplete, onError);\n    } catch (error) {\n      console.error('Error in submitRatingToDatabase:', error);\n      notification.error('Failed to submit feedback. Please try again.');\n    }\n  };\n\n  // Get user message that corresponds to this AI response\n  const getUserMessageForResponse = currentIdx => {\n    // Look for the previous user message\n    for (let i = currentIdx - 1; i >= 0; i--) {\n      var _messages$i;\n      if (((_messages$i = messages[i]) === null || _messages$i === void 0 ? void 0 : _messages$i.from) === 'user') {\n        return messages[i];\n      }\n    }\n    return null;\n  };\n\n  // Handle thumbs up helpful click\n  const handleThumbsUpHelpfulClick = idx => {\n    var _messages$idx;\n    // Check if already rated\n    if ((_messages$idx = messages[idx]) === null || _messages$idx === void 0 ? void 0 : _messages$idx.ratingSubmitted) {\n      return;\n    }\n    setHelpfulMap(prev => ({\n      ...prev,\n      [idx]: !prev[idx]\n    }));\n    setMessages(prev => prev.map((msg, i) => i === idx ? {\n      ...msg,\n      showFeedbackOptions: false,\n      ratingSubmitted: true,\n      ratingType: 'positive'\n    } : msg));\n\n    // Submit positive rating to database\n    const userMessage = getUserMessageForResponse(idx);\n    const currentMessage = messages[idx];\n    const ratingData = {\n      session_id: sessionId,\n      usr_prompt: (userMessage === null || userMessage === void 0 ? void 0 : userMessage.text) || '',\n      va_response: currentMessage,\n      good_rating: true,\n      bad_rating: false,\n      additional_comments: '',\n      sugg_feedback: ''\n    };\n    submitRatingToDatabase(ratingData);\n  };\n\n  // Toggle feedback section\n  const toggleFeedbackSection = idx => {\n    var _messages$idx2;\n    // Check if already rated\n    if ((_messages$idx2 = messages[idx]) === null || _messages$idx2 === void 0 ? void 0 : _messages$idx2.ratingSubmitted) {\n      return;\n    }\n    setHelpfulMap(prev => ({\n      ...prev,\n      [idx]: false\n    }));\n    setMessages(prev => prev.map((msg, i) => i === idx ? {\n      ...msg,\n      showFeedbackOptions: !msg.showFeedbackOptions\n    } : msg));\n  };\n\n  // Handle feedback type selection\n  const handleFeedbackClick = (idx, selectedType) => {\n    setMessages(prevMessages => prevMessages.map((msg, i) => {\n      if (i !== idx) return msg;\n      const currentTypes = Array.isArray(msg.feedbackType) ? msg.feedbackType : [];\n      const alreadySelected = currentTypes.includes(selectedType);\n      const updatedTypes = alreadySelected ? currentTypes.filter(type => type !== selectedType) // deselect\n      : [...currentTypes, selectedType]; // select\n\n      return {\n        ...msg,\n        feedbackType: updatedTypes,\n        showFeedbackOptions: true,\n        customFeedback: msg.customFeedback || ''\n      };\n    }));\n  };\n\n  // Handle custom feedback text change\n  const handleCustomFeedbackChange = (index, value) => {\n    setMessages(prevMessages => prevMessages.map((msg, i) => i === index ? {\n      ...msg,\n      customFeedback: value\n    } : msg));\n  };\n\n  // Handle feedback submission\n  const handleCustomFeedbackSubmit = index => {\n    const currentMessage = messages[index];\n    const userMessage = getUserMessageForResponse(index);\n\n    // Prepare detailed feedback data\n    const feedbackTypes = (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.feedbackType) || [];\n    const customFeedback = (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.customFeedback) || '';\n\n    // Map feedback types to readable format using the same array\n    const feedbackMapping = FEEDBACK_OPTIONS.reduce((acc, option) => {\n      acc[option.key] = option.label;\n      return acc;\n    }, {});\n    const suggFeedback = feedbackTypes.map(type => feedbackMapping[type] || type).join(', ');\n    const ratingData = {\n      session_id: sessionId,\n      usr_prompt: (userMessage === null || userMessage === void 0 ? void 0 : userMessage.text) || '',\n      va_response: {\n        text: (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.text) || '',\n        block: (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.block) || null,\n        time: (currentMessage === null || currentMessage === void 0 ? void 0 : currentMessage.time) || ''\n      },\n      good_rating: false,\n      bad_rating: true,\n      additional_comments: customFeedback,\n      sugg_feedback: suggFeedback\n    };\n\n    // Submit to database\n    submitRatingToDatabase(ratingData);\n    setHelpfulMap(prev => ({\n      ...prev,\n      [index]: false\n    }));\n    setMessages(prev => prev.map((msg, i) => i === index ? {\n      ...msg,\n      showFeedbackOptions: false,\n      feedbackType: null,\n      customFeedback: '',\n      feedbackSubmitted: true,\n      ratingSubmitted: true,\n      ratingType: 'negative'\n    } : msg));\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-message-wrapper ai\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-message-bubble\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 17\n    }\n  }, isGenerating ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-initial-loading\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 25\n    }\n  }) // Loading animation\n  : /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      maxHeight: '350px',\n      overflowY: 'auto'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(RenderAgentResponseBlock, {\n    text: message.text,\n    block: message === null || message === void 0 ? void 0 : message.block,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 29\n    }\n  }))), !message.isWaiting && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"gx-d-flex gx-align-items-center gx-justify-content-between gx-mt-1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-bubble-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-feedback-bubble\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: `wy-acb-feedback-btn helpful ${helpfulMap[idx] || message.ratingType === 'positive' ? 'wy-acb-feedback-helpful-btn-active' : ''} ${message.showFeedbackOptions || message.feedbackSubmitted ? 'gx-d-none' : ''} ${message.ratingSubmitted ? 'disabled' : ''}`,\n    title: message.ratingSubmitted ? 'Already rated' : 'Helpful',\n    disabled: message.ratingSubmitted,\n    onClick: () => handleThumbsUpHelpfulClick(idx),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Tooltip, {\n    placement: \"bottom\",\n    title: \"Helpful\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(GoThumbsup, {\n    stroke: helpfulMap[idx] || message.ratingType === 'positive' ? 'green' : undefined,\n    strokeWidth: helpfulMap[idx] || message.ratingType === 'positive' ? 1 : 0,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 41\n    }\n  }))), /*#__PURE__*/React.createElement(\"button\", {\n    className: `wy-acb-feedback-btn not-helpful\n                                                                            ${message.feedbackSubmitted || message.showFeedbackOptions || message.ratingType === 'negative' ? 'wy-acb-feedback-not-helpful-btn-active' : ''}\n                                                                            ${helpfulMap[idx] ? 'gx-d-none' : ''} ${message.ratingSubmitted ? 'disabled' : ''}`,\n    title: message.ratingSubmitted ? 'Already rated' : 'Not Helpful',\n    disabled: message.ratingSubmitted,\n    onClick: () => toggleFeedbackSection(idx),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(Tooltip, {\n    placement: \"bottom\",\n    title: \"Not Helpful\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(GoThumbsdown, {\n    color: message.showFeedbackOptions || message.ratingType === 'negative' ? 'black' : undefined,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 41\n    }\n  })))), message.feedbackSubmitted && /*#__PURE__*/React.createElement(Alert, {\n    showIcon: true,\n    message: \"Thank you for your feedback!\",\n    type: \"success\",\n    className: \"gx-mt-2 wy-acb-full-width-chat-area gx-fs-sm\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"wy-acb-timestamp gx-d-none\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 25\n    }\n  }, message.time)), message.showFeedbackOptions && /*#__PURE__*/React.createElement(FeedbackSection, {\n    idx: idx,\n    msg: message,\n    toggleFeedbackSection: toggleFeedbackSection,\n    handleFeedbackClick: handleFeedbackClick,\n    handleCustomFeedbackChange: handleCustomFeedbackChange,\n    handleCustomFeedbackSubmit: handleCustomFeedbackSubmit,\n    messages: messages,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 21\n    }\n  })));\n};\nexport default AgentMessage;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "GoThumbsup", "GoThumbsdown", "GoComment", "GoSync", "<PERSON><PERSON>", "<PERSON><PERSON>", "Input", "message", "notification", "<PERSON><PERSON><PERSON>", "MdOutlineClose", "RenderAgentResponseBlock", "http_utils", "FEEDBACK_OPTIONS", "key", "label", "FeedbackSection", "idx", "msg", "toggleFeedbackSection", "handleFeedbackClick", "handleCustomFeedbackChange", "handleCustomFeedbackSubmit", "messages", "wrapperRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "showFeedbackOptions", "setTimeout", "isLastMessage", "length", "chatContainer", "closest", "scrollTo", "top", "scrollHeight", "behavior", "scrollIntoView", "block", "createElement", "ref", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "option", "_msg$feedbackType", "type", "feedbackType", "includes", "TextArea", "value", "customFeedback", "placeholder", "rows", "onChange", "e", "danger", "AgentMessage", "isGenerating", "helpfulMap", "setHelpfulMap", "setMessages", "sessionId", "submitRatingToDatabase", "ratingData", "onComplete", "resp", "console", "log", "onError", "error", "performPostCall", "getUserMessageForResponse", "currentIdx", "i", "_messages$i", "from", "handleThumbsUpHelpfulClick", "_messages$idx", "ratingSubmitted", "prev", "ratingType", "userMessage", "currentMessage", "session_id", "usr_prompt", "text", "va_response", "good_rating", "bad_rating", "additional_comments", "sugg_feedback", "_messages$idx2", "selectedType", "prevMessages", "currentTypes", "Array", "isArray", "alreadySelected", "updatedTypes", "filter", "index", "feedbackTypes", "feedbackMapping", "reduce", "acc", "suggFeedback", "join", "time", "feedbackSubmitted", "style", "maxHeight", "overflowY", "isWaiting", "title", "disabled", "placement", "stroke", "undefined", "strokeWidth", "color", "showIcon"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/components/WIFY/AiChatBot/AgentMessage.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { GoThumbsup, GoThumbsdown, GoComment, GoSync } from 'react-icons/go';\r\nimport { Alert, Button, Input, message as notification, Tooltip } from 'antd';\r\nimport { MdOutlineClose } from 'react-icons/md';\r\nimport RenderAgentResponseBlock from './ReactMarkDown';\r\nimport http_utils from '../../../util/http_utils';\r\n\r\n// Feedback options configuration\r\nconst FEEDBACK_OPTIONS = [\r\n    {\r\n        key: 'confused',\r\n        label: \"Didn't fully follow instructions\",\r\n    },\r\n    {\r\n        key: 'irrelevant',\r\n        label: 'Inaccurate result',\r\n    },\r\n    {\r\n        key: 'not_relevant_2',\r\n        label: 'This does not make sense',\r\n    },\r\n];\r\n\r\n// FeedbackSection component moved outside to prevent re-creation on every render\r\nconst FeedbackSection = ({\r\n    idx,\r\n    msg,\r\n    toggleFeedbackSection,\r\n    handleFeedbackClick,\r\n    handleCustomFeedbackChange,\r\n    handleCustomFeedbackSubmit,\r\n    messages,\r\n}) => {\r\n    const wrapperRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        function handleClickOutside(event) {\r\n            if (\r\n                wrapperRef.current &&\r\n                !wrapperRef.current.contains(event.target)\r\n            ) {\r\n                toggleFeedbackSection(idx); // Close feedback section\r\n            }\r\n        }\r\n\r\n        document.addEventListener('click', handleClickOutside);\r\n        return () => {\r\n            document.removeEventListener('click', handleClickOutside);\r\n        };\r\n    }, [idx, toggleFeedbackSection]);\r\n\r\n    // Scroll feedback section into view when it opens\r\n    useEffect(() => {\r\n        if (msg.showFeedbackOptions && wrapperRef.current) {\r\n            // Small delay to ensure the feedback section is fully rendered\r\n            setTimeout(() => {\r\n                const isLastMessage = idx === messages.length - 1;\r\n\r\n                if (isLastMessage) {\r\n                    // If this is the last message, scroll to bottom of container\r\n                    const chatContainer =\r\n                        wrapperRef.current.closest('.wy-acb-messages');\r\n                    if (chatContainer) {\r\n                        chatContainer.scrollTo({\r\n                            top: chatContainer.scrollHeight,\r\n                            behavior: 'smooth',\r\n                        });\r\n                    }\r\n                } else {\r\n                    // For other messages, scroll feedback section into view\r\n                    wrapperRef.current.scrollIntoView({\r\n                        behavior: 'smooth',\r\n                        block: 'nearest',\r\n                    });\r\n                }\r\n            }, 100);\r\n        }\r\n    }, [msg.showFeedbackOptions, idx, messages.length]);\r\n\r\n    return (\r\n        msg.showFeedbackOptions && (\r\n            <div ref={wrapperRef} className=\"wy-acb-feedback-section-wrapper\">\r\n                <div className=\"wy-acb-feedback-section-header gx-mb-3 gx-position-relative\">\r\n                    <div className=\"wy-acb-title-decor\"></div>\r\n                    <div>Tell us more...</div>\r\n                    <MdOutlineClose\r\n                        className=\"gx-text-red wy-cursor-pointer gx-fs-xl\"\r\n                        onClick={() => toggleFeedbackSection(idx)}\r\n                    />\r\n                </div>\r\n                <div>\r\n                    <div>\r\n                        <div className=\"wy-acb-feedback-section\">\r\n                            {FEEDBACK_OPTIONS.map((option) => (\r\n                                <Button\r\n                                    key={option.key}\r\n                                    block\r\n                                    type={\r\n                                        msg.feedbackType?.includes(option.key)\r\n                                            ? 'primary'\r\n                                            : 'ghost'\r\n                                    }\r\n                                    onClick={() =>\r\n                                        handleFeedbackClick(idx, option.key)\r\n                                    }\r\n                                    className=\"gx-mr-0\"\r\n                                >\r\n                                    {option.label}\r\n                                </Button>\r\n                            ))}\r\n                        </div>\r\n                        <div className=\"gx-mb-2\">\r\n                            <Input.TextArea\r\n                                value={msg.customFeedback || ''}\r\n                                placeholder=\"Please describe what could be improved... (optional)\"\r\n                                rows={2}\r\n                                className=\"wy-acb-textarea\"\r\n                                onChange={(e) =>\r\n                                    handleCustomFeedbackChange(\r\n                                        idx,\r\n                                        e.target.value\r\n                                    )\r\n                                }\r\n                            />\r\n                        </div>\r\n                        <div className=\"gx-text-right gx-mt-1\">\r\n                            <Button\r\n                                danger\r\n                                type=\"text\"\r\n                                onClick={() => toggleFeedbackSection(idx)}\r\n                                className=\"gx-mr-2 gx-mt-1 gx-mb-0\"\r\n                            >\r\n                                Cancel\r\n                            </Button>\r\n                            <Button\r\n                                type=\"primary\"\r\n                                onClick={() => handleCustomFeedbackSubmit(idx)}\r\n                                className=\"gx-mt-1 gx-mb-0\"\r\n                            >\r\n                                Submit\r\n                            </Button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        )\r\n    );\r\n};\r\n\r\nconst AgentMessage = ({\r\n    message,\r\n    isGenerating,\r\n    idx,\r\n    helpfulMap,\r\n    setHelpfulMap,\r\n    messages,\r\n    setMessages,\r\n    sessionId,\r\n}) => {\r\n    // API call to save rating to database\r\n    const submitRatingToDatabase = async (ratingData) => {\r\n        try {\r\n            // console.log('Submitting rating to database:', ratingData);\r\n\r\n            const onComplete = (resp) => {\r\n                console.log('Rating submitted successfully:', resp);\r\n                // notification.success('Thank you for your feedback!');\r\n            };\r\n\r\n            const onError = (error) => {\r\n                console.error('Error submitting rating:', error);\r\n                notification.error(\r\n                    'Failed to submit feedback. Please try again.'\r\n                );\r\n            };\r\n\r\n            http_utils.performPostCall(\r\n                '/ai-chatbot/va-chatbot-resp-rating',\r\n                ratingData,\r\n                onComplete,\r\n                onError\r\n            );\r\n        } catch (error) {\r\n            console.error('Error in submitRatingToDatabase:', error);\r\n            notification.error('Failed to submit feedback. Please try again.');\r\n        }\r\n    };\r\n\r\n    // Get user message that corresponds to this AI response\r\n    const getUserMessageForResponse = (currentIdx) => {\r\n        // Look for the previous user message\r\n        for (let i = currentIdx - 1; i >= 0; i--) {\r\n            if (messages[i]?.from === 'user') {\r\n                return messages[i];\r\n            }\r\n        }\r\n        return null;\r\n    };\r\n\r\n    // Handle thumbs up helpful click\r\n    const handleThumbsUpHelpfulClick = (idx) => {\r\n        // Check if already rated\r\n        if (messages[idx]?.ratingSubmitted) {\r\n            return;\r\n        }\r\n\r\n        setHelpfulMap((prev) => ({\r\n            ...prev,\r\n            [idx]: !prev[idx],\r\n        }));\r\n\r\n        setMessages((prev) =>\r\n            prev.map((msg, i) =>\r\n                i === idx\r\n                    ? {\r\n                          ...msg,\r\n                          showFeedbackOptions: false,\r\n                          ratingSubmitted: true,\r\n                          ratingType: 'positive',\r\n                      }\r\n                    : msg\r\n            )\r\n        );\r\n\r\n        // Submit positive rating to database\r\n        const userMessage = getUserMessageForResponse(idx);\r\n        const currentMessage = messages[idx];\r\n\r\n        const ratingData = {\r\n            session_id: sessionId,\r\n            usr_prompt: userMessage?.text || '',\r\n            va_response: currentMessage,\r\n            good_rating: true,\r\n            bad_rating: false,\r\n            additional_comments: '',\r\n            sugg_feedback: '',\r\n        };\r\n\r\n        submitRatingToDatabase(ratingData);\r\n    };\r\n\r\n    // Toggle feedback section\r\n    const toggleFeedbackSection = (idx) => {\r\n        // Check if already rated\r\n        if (messages[idx]?.ratingSubmitted) {\r\n            return;\r\n        }\r\n\r\n        setHelpfulMap((prev) => ({\r\n            ...prev,\r\n            [idx]: false,\r\n        }));\r\n\r\n        setMessages((prev) =>\r\n            prev.map((msg, i) =>\r\n                i === idx\r\n                    ? { ...msg, showFeedbackOptions: !msg.showFeedbackOptions }\r\n                    : msg\r\n            )\r\n        );\r\n    };\r\n\r\n    // Handle feedback type selection\r\n    const handleFeedbackClick = (idx, selectedType) => {\r\n        setMessages((prevMessages) =>\r\n            prevMessages.map((msg, i) => {\r\n                if (i !== idx) return msg;\r\n\r\n                const currentTypes = Array.isArray(msg.feedbackType)\r\n                    ? msg.feedbackType\r\n                    : [];\r\n                const alreadySelected = currentTypes.includes(selectedType);\r\n\r\n                const updatedTypes = alreadySelected\r\n                    ? currentTypes.filter((type) => type !== selectedType) // deselect\r\n                    : [...currentTypes, selectedType]; // select\r\n\r\n                return {\r\n                    ...msg,\r\n                    feedbackType: updatedTypes,\r\n                    showFeedbackOptions: true,\r\n                    customFeedback: msg.customFeedback || '',\r\n                };\r\n            })\r\n        );\r\n    };\r\n\r\n    // Handle custom feedback text change\r\n    const handleCustomFeedbackChange = (index, value) => {\r\n        setMessages((prevMessages) =>\r\n            prevMessages.map((msg, i) =>\r\n                i === index ? { ...msg, customFeedback: value } : msg\r\n            )\r\n        );\r\n    };\r\n\r\n    // Handle feedback submission\r\n    const handleCustomFeedbackSubmit = (index) => {\r\n        const currentMessage = messages[index];\r\n        const userMessage = getUserMessageForResponse(index);\r\n\r\n        // Prepare detailed feedback data\r\n        const feedbackTypes = currentMessage?.feedbackType || [];\r\n        const customFeedback = currentMessage?.customFeedback || '';\r\n\r\n        // Map feedback types to readable format using the same array\r\n        const feedbackMapping = FEEDBACK_OPTIONS.reduce((acc, option) => {\r\n            acc[option.key] = option.label;\r\n            return acc;\r\n        }, {});\r\n\r\n        const suggFeedback = feedbackTypes\r\n            .map((type) => feedbackMapping[type] || type)\r\n            .join(', ');\r\n\r\n        const ratingData = {\r\n            session_id: sessionId,\r\n            usr_prompt: userMessage?.text || '',\r\n            va_response: {\r\n                text: currentMessage?.text || '',\r\n                block: currentMessage?.block || null,\r\n                time: currentMessage?.time || '',\r\n            },\r\n            good_rating: false,\r\n            bad_rating: true,\r\n            additional_comments: customFeedback,\r\n            sugg_feedback: suggFeedback,\r\n        };\r\n\r\n        // Submit to database\r\n        submitRatingToDatabase(ratingData);\r\n\r\n        setHelpfulMap((prev) => ({\r\n            ...prev,\r\n            [index]: false,\r\n        }));\r\n\r\n        setMessages((prev) =>\r\n            prev.map((msg, i) =>\r\n                i === index\r\n                    ? {\r\n                          ...msg,\r\n                          showFeedbackOptions: false,\r\n                          feedbackType: null,\r\n                          customFeedback: '',\r\n                          feedbackSubmitted: true,\r\n                          ratingSubmitted: true,\r\n                          ratingType: 'negative',\r\n                      }\r\n                    : msg\r\n            )\r\n        );\r\n    };\r\n\r\n    return (\r\n        <div className=\"wy-acb-message-wrapper ai\">\r\n            <div className=\"message-wrapper\">\r\n                <div className=\"wy-acb-message-bubble\">\r\n                    {isGenerating ? (\r\n                        <div className=\"wy-acb-initial-loading\"></div> // Loading animation\r\n                    ) : (\r\n                        <div style={{ maxHeight: '350px', overflowY: 'auto' }}>\r\n                            <RenderAgentResponseBlock\r\n                                text={message.text}\r\n                                block={message?.block}\r\n                            />\r\n                        </div>\r\n                    )}\r\n                </div>\r\n\r\n                {!message.isWaiting && (\r\n                    <div className=\"gx-d-flex gx-align-items-center gx-justify-content-between gx-mt-1\">\r\n                        <div className=\"wy-acb-feedback-bubble-wrapper\">\r\n                            <div className=\"wy-acb-feedback-bubble\">\r\n                                <button\r\n                                    className={`wy-acb-feedback-btn helpful ${\r\n                                        helpfulMap[idx] ||\r\n                                        message.ratingType === 'positive'\r\n                                            ? 'wy-acb-feedback-helpful-btn-active'\r\n                                            : ''\r\n                                    } ${message.showFeedbackOptions || message.feedbackSubmitted ? 'gx-d-none' : ''} ${\r\n                                        message.ratingSubmitted\r\n                                            ? 'disabled'\r\n                                            : ''\r\n                                    }`}\r\n                                    title={\r\n                                        message.ratingSubmitted\r\n                                            ? 'Already rated'\r\n                                            : 'Helpful'\r\n                                    }\r\n                                    disabled={message.ratingSubmitted}\r\n                                    onClick={() =>\r\n                                        handleThumbsUpHelpfulClick(idx)\r\n                                    }\r\n                                >\r\n                                    <Tooltip placement=\"bottom\" title=\"Helpful\">\r\n                                        <GoThumbsup\r\n                                            stroke={\r\n                                                helpfulMap[idx] ||\r\n                                                message.ratingType ===\r\n                                                    'positive'\r\n                                                    ? 'green'\r\n                                                    : undefined\r\n                                            }\r\n                                            strokeWidth={\r\n                                                helpfulMap[idx] ||\r\n                                                message.ratingType ===\r\n                                                    'positive'\r\n                                                    ? 1\r\n                                                    : 0\r\n                                            }\r\n                                        />\r\n                                    </Tooltip>\r\n                                </button>\r\n\r\n                                <button\r\n                                    className={`wy-acb-feedback-btn not-helpful\r\n                                                                            ${message.feedbackSubmitted || message.showFeedbackOptions || message.ratingType === 'negative' ? 'wy-acb-feedback-not-helpful-btn-active' : ''}\r\n                                                                            ${helpfulMap[idx] ? 'gx-d-none' : ''} ${\r\n                                                                                message.ratingSubmitted\r\n                                                                                    ? 'disabled'\r\n                                                                                    : ''\r\n                                                                            }`}\r\n                                    title={\r\n                                        message.ratingSubmitted\r\n                                            ? 'Already rated'\r\n                                            : 'Not Helpful'\r\n                                    }\r\n                                    disabled={message.ratingSubmitted}\r\n                                    onClick={() => toggleFeedbackSection(idx)}\r\n                                >\r\n                                    <Tooltip\r\n                                        placement=\"bottom\"\r\n                                        title=\"Not Helpful\"\r\n                                    >\r\n                                        <GoThumbsdown\r\n                                            color={\r\n                                                message.showFeedbackOptions ||\r\n                                                message.ratingType ===\r\n                                                    'negative'\r\n                                                    ? 'black'\r\n                                                    : undefined\r\n                                            }\r\n                                        />\r\n                                    </Tooltip>\r\n                                </button>\r\n                            </div>\r\n                            {message.feedbackSubmitted && (\r\n                                <Alert\r\n                                    showIcon\r\n                                    message=\"Thank you for your feedback!\"\r\n                                    type=\"success\"\r\n                                    className=\"gx-mt-2 wy-acb-full-width-chat-area gx-fs-sm\"\r\n                                />\r\n                            )}\r\n                        </div>\r\n                        <div className=\"wy-acb-timestamp gx-d-none\">\r\n                            {message.time}\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {/* Feedback Selection Section */}\r\n                {message.showFeedbackOptions && (\r\n                    <FeedbackSection\r\n                        idx={idx}\r\n                        msg={message}\r\n                        toggleFeedbackSection={toggleFeedbackSection}\r\n                        handleFeedbackClick={handleFeedbackClick}\r\n                        handleCustomFeedbackChange={handleCustomFeedbackChange}\r\n                        handleCustomFeedbackSubmit={handleCustomFeedbackSubmit}\r\n                        messages={messages}\r\n                    />\r\n                )}\r\n\r\n                {/* Show feedback buttons when AI has completed response */}\r\n                {/* {!isGenerating && (\r\n                    <div className=\"wy-acb-feedback-bubble\">\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn helpful\"\r\n                            title=\"Helpful\"\r\n                        >\r\n                            <GoThumbsup />\r\n                        </button>\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn not-helpful\"\r\n                            title=\"Not Helpful\"\r\n                        >\r\n                            <GoThumbsdown />\r\n                        </button>\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn comment\"\r\n                            title=\"Comments\"\r\n                        >\r\n                            <GoComment />\r\n                        </button>\r\n                        <button\r\n                            className=\"wy-acb-feedback-btn regenerate\"\r\n                            title=\"Regenerate\"\r\n                        >\r\n                            <GoSync />\r\n                        </button>\r\n                    </div>\r\n                )} */}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AgentMessage;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;AAC5E,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,IAAIC,YAAY,EAAEC,OAAO,QAAQ,MAAM;AAC7E,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAOC,wBAAwB,MAAM,iBAAiB;AACtD,OAAOC,UAAU,MAAM,0BAA0B;;AAEjD;AACA,MAAMC,gBAAgB,GAAG,CACrB;EACIC,GAAG,EAAE,UAAU;EACfC,KAAK,EAAE;AACX,CAAC,EACD;EACID,GAAG,EAAE,YAAY;EACjBC,KAAK,EAAE;AACX,CAAC,EACD;EACID,GAAG,EAAE,gBAAgB;EACrBC,KAAK,EAAE;AACX,CAAC,CACJ;;AAED;AACA,MAAMC,eAAe,GAAGA,CAAC;EACrBC,GAAG;EACHC,GAAG;EACHC,qBAAqB;EACrBC,mBAAmB;EACnBC,0BAA0B;EAC1BC,0BAA0B;EAC1BC;AACJ,CAAC,KAAK;EACF,MAAMC,UAAU,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAE/BC,SAAS,CAAC,MAAM;IACZ,SAAS0B,kBAAkBA,CAACC,KAAK,EAAE;MAC/B,IACIF,UAAU,CAACG,OAAO,IAClB,CAACH,UAAU,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAC5C;QACEV,qBAAqB,CAACF,GAAG,CAAC,CAAC,CAAC;MAChC;IACJ;IAEAa,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEN,kBAAkB,CAAC;IACtD,OAAO,MAAM;MACTK,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEP,kBAAkB,CAAC;IAC7D,CAAC;EACL,CAAC,EAAE,CAACR,GAAG,EAAEE,qBAAqB,CAAC,CAAC;;EAEhC;EACApB,SAAS,CAAC,MAAM;IACZ,IAAImB,GAAG,CAACe,mBAAmB,IAAIT,UAAU,CAACG,OAAO,EAAE;MAC/C;MACAO,UAAU,CAAC,MAAM;QACb,MAAMC,aAAa,GAAGlB,GAAG,KAAKM,QAAQ,CAACa,MAAM,GAAG,CAAC;QAEjD,IAAID,aAAa,EAAE;UACf;UACA,MAAME,aAAa,GACfb,UAAU,CAACG,OAAO,CAACW,OAAO,CAAC,kBAAkB,CAAC;UAClD,IAAID,aAAa,EAAE;YACfA,aAAa,CAACE,QAAQ,CAAC;cACnBC,GAAG,EAAEH,aAAa,CAACI,YAAY;cAC/BC,QAAQ,EAAE;YACd,CAAC,CAAC;UACN;QACJ,CAAC,MAAM;UACH;UACAlB,UAAU,CAACG,OAAO,CAACgB,cAAc,CAAC;YAC9BD,QAAQ,EAAE,QAAQ;YAClBE,KAAK,EAAE;UACX,CAAC,CAAC;QACN;MACJ,CAAC,EAAE,GAAG,CAAC;IACX;EACJ,CAAC,EAAE,CAAC1B,GAAG,CAACe,mBAAmB,EAAEhB,GAAG,EAAEM,QAAQ,CAACa,MAAM,CAAC,CAAC;EAEnD,OACIlB,GAAG,CAACe,mBAAmB,iBACnBrC,KAAA,CAAAiD,aAAA;IAAKC,GAAG,EAAEtB,UAAW;IAACuB,SAAS,EAAC,iCAAiC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7DzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,6DAA6D;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxEzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAAC,eAC1CzD,KAAA,CAAAiD,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,iBAAoB,CAAC,eAC1BzD,KAAA,CAAAiD,aAAA,CAACnC,cAAc;IACXqC,SAAS,EAAC,wCAAwC;IAClDO,OAAO,EAAEA,CAAA,KAAMnC,qBAAqB,CAACF,GAAG,CAAE;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC7C,CACA,CAAC,eACNzD,KAAA,CAAAiD,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzD,KAAA,CAAAiD,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACnCxC,gBAAgB,CAAC0C,GAAG,CAAEC,MAAM;IAAA,IAAAC,iBAAA;IAAA,oBACzB7D,KAAA,CAAAiD,aAAA,CAACxC,MAAM;MACHS,GAAG,EAAE0C,MAAM,CAAC1C,GAAI;MAChB8B,KAAK;MACLc,IAAI,EACA,EAAAD,iBAAA,GAAAvC,GAAG,CAACyC,YAAY,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBG,QAAQ,CAACJ,MAAM,CAAC1C,GAAG,CAAC,IAChC,SAAS,GACT,OACT;MACDwC,OAAO,EAAEA,CAAA,KACLlC,mBAAmB,CAACH,GAAG,EAAEuC,MAAM,CAAC1C,GAAG,CACtC;MACDiC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAElBG,MAAM,CAACzC,KACJ,CAAC;EAAA,CACZ,CACA,CAAC,eACNnB,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBzD,KAAA,CAAAiD,aAAA,CAACvC,KAAK,CAACuD,QAAQ;IACXC,KAAK,EAAE5C,GAAG,CAAC6C,cAAc,IAAI,EAAG;IAChCC,WAAW,EAAC,sDAAsD;IAClEC,IAAI,EAAE,CAAE;IACRlB,SAAS,EAAC,iBAAiB;IAC3BmB,QAAQ,EAAGC,CAAC,IACR9C,0BAA0B,CACtBJ,GAAG,EACHkD,CAAC,CAACtC,MAAM,CAACiC,KACb,CACH;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACJ,CACA,CAAC,eACNzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCzD,KAAA,CAAAiD,aAAA,CAACxC,MAAM;IACH+D,MAAM;IACNV,IAAI,EAAC,MAAM;IACXJ,OAAO,EAAEA,CAAA,KAAMnC,qBAAqB,CAACF,GAAG,CAAE;IAC1C8B,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,QAEO,CAAC,eACTzD,KAAA,CAAAiD,aAAA,CAACxC,MAAM;IACHqD,IAAI,EAAC,SAAS;IACdJ,OAAO,EAAEA,CAAA,KAAMhC,0BAA0B,CAACL,GAAG,CAAE;IAC/C8B,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B,QAEO,CACP,CACJ,CACJ,CACJ,CACR;AAET,CAAC;AAED,MAAMgB,YAAY,GAAGA,CAAC;EAClB9D,OAAO;EACP+D,YAAY;EACZrD,GAAG;EACHsD,UAAU;EACVC,aAAa;EACbjD,QAAQ;EACRkD,WAAW;EACXC;AACJ,CAAC,KAAK;EACF;EACA,MAAMC,sBAAsB,GAAG,MAAOC,UAAU,IAAK;IACjD,IAAI;MACA;;MAEA,MAAMC,UAAU,GAAIC,IAAI,IAAK;QACzBC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEF,IAAI,CAAC;QACnD;MACJ,CAAC;MAED,MAAMG,OAAO,GAAIC,KAAK,IAAK;QACvBH,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD1E,YAAY,CAAC0E,KAAK,CACd,8CACJ,CAAC;MACL,CAAC;MAEDtE,UAAU,CAACuE,eAAe,CACtB,oCAAoC,EACpCP,UAAU,EACVC,UAAU,EACVI,OACJ,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD1E,YAAY,CAAC0E,KAAK,CAAC,8CAA8C,CAAC;IACtE;EACJ,CAAC;;EAED;EACA,MAAME,yBAAyB,GAAIC,UAAU,IAAK;IAC9C;IACA,KAAK,IAAIC,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAAA,IAAAC,WAAA;MACtC,IAAI,EAAAA,WAAA,GAAAhE,QAAQ,CAAC+D,CAAC,CAAC,cAAAC,WAAA,uBAAXA,WAAA,CAAaC,IAAI,MAAK,MAAM,EAAE;QAC9B,OAAOjE,QAAQ,CAAC+D,CAAC,CAAC;MACtB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAMG,0BAA0B,GAAIxE,GAAG,IAAK;IAAA,IAAAyE,aAAA;IACxC;IACA,KAAAA,aAAA,GAAInE,QAAQ,CAACN,GAAG,CAAC,cAAAyE,aAAA,uBAAbA,aAAA,CAAeC,eAAe,EAAE;MAChC;IACJ;IAEAnB,aAAa,CAAEoB,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAAC3E,GAAG,GAAG,CAAC2E,IAAI,CAAC3E,GAAG;IACpB,CAAC,CAAC,CAAC;IAEHwD,WAAW,CAAEmB,IAAI,IACbA,IAAI,CAACrC,GAAG,CAAC,CAACrC,GAAG,EAAEoE,CAAC,KACZA,CAAC,KAAKrE,GAAG,GACH;MACI,GAAGC,GAAG;MACNe,mBAAmB,EAAE,KAAK;MAC1B0D,eAAe,EAAE,IAAI;MACrBE,UAAU,EAAE;IAChB,CAAC,GACD3E,GACV,CACJ,CAAC;;IAED;IACA,MAAM4E,WAAW,GAAGV,yBAAyB,CAACnE,GAAG,CAAC;IAClD,MAAM8E,cAAc,GAAGxE,QAAQ,CAACN,GAAG,CAAC;IAEpC,MAAM2D,UAAU,GAAG;MACfoB,UAAU,EAAEtB,SAAS;MACrBuB,UAAU,EAAE,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,KAAI,EAAE;MACnCC,WAAW,EAAEJ,cAAc;MAC3BK,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,KAAK;MACjBC,mBAAmB,EAAE,EAAE;MACvBC,aAAa,EAAE;IACnB,CAAC;IAED5B,sBAAsB,CAACC,UAAU,CAAC;EACtC,CAAC;;EAED;EACA,MAAMzD,qBAAqB,GAAIF,GAAG,IAAK;IAAA,IAAAuF,cAAA;IACnC;IACA,KAAAA,cAAA,GAAIjF,QAAQ,CAACN,GAAG,CAAC,cAAAuF,cAAA,uBAAbA,cAAA,CAAeb,eAAe,EAAE;MAChC;IACJ;IAEAnB,aAAa,CAAEoB,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAAC3E,GAAG,GAAG;IACX,CAAC,CAAC,CAAC;IAEHwD,WAAW,CAAEmB,IAAI,IACbA,IAAI,CAACrC,GAAG,CAAC,CAACrC,GAAG,EAAEoE,CAAC,KACZA,CAAC,KAAKrE,GAAG,GACH;MAAE,GAAGC,GAAG;MAAEe,mBAAmB,EAAE,CAACf,GAAG,CAACe;IAAoB,CAAC,GACzDf,GACV,CACJ,CAAC;EACL,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAGA,CAACH,GAAG,EAAEwF,YAAY,KAAK;IAC/ChC,WAAW,CAAEiC,YAAY,IACrBA,YAAY,CAACnD,GAAG,CAAC,CAACrC,GAAG,EAAEoE,CAAC,KAAK;MACzB,IAAIA,CAAC,KAAKrE,GAAG,EAAE,OAAOC,GAAG;MAEzB,MAAMyF,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAC3F,GAAG,CAACyC,YAAY,CAAC,GAC9CzC,GAAG,CAACyC,YAAY,GAChB,EAAE;MACR,MAAMmD,eAAe,GAAGH,YAAY,CAAC/C,QAAQ,CAAC6C,YAAY,CAAC;MAE3D,MAAMM,YAAY,GAAGD,eAAe,GAC9BH,YAAY,CAACK,MAAM,CAAEtD,IAAI,IAAKA,IAAI,KAAK+C,YAAY,CAAC,CAAC;MAAA,EACrD,CAAC,GAAGE,YAAY,EAAEF,YAAY,CAAC,CAAC,CAAC;;MAEvC,OAAO;QACH,GAAGvF,GAAG;QACNyC,YAAY,EAAEoD,YAAY;QAC1B9E,mBAAmB,EAAE,IAAI;QACzB8B,cAAc,EAAE7C,GAAG,CAAC6C,cAAc,IAAI;MAC1C,CAAC;IACL,CAAC,CACL,CAAC;EACL,CAAC;;EAED;EACA,MAAM1C,0BAA0B,GAAGA,CAAC4F,KAAK,EAAEnD,KAAK,KAAK;IACjDW,WAAW,CAAEiC,YAAY,IACrBA,YAAY,CAACnD,GAAG,CAAC,CAACrC,GAAG,EAAEoE,CAAC,KACpBA,CAAC,KAAK2B,KAAK,GAAG;MAAE,GAAG/F,GAAG;MAAE6C,cAAc,EAAED;IAAM,CAAC,GAAG5C,GACtD,CACJ,CAAC;EACL,CAAC;;EAED;EACA,MAAMI,0BAA0B,GAAI2F,KAAK,IAAK;IAC1C,MAAMlB,cAAc,GAAGxE,QAAQ,CAAC0F,KAAK,CAAC;IACtC,MAAMnB,WAAW,GAAGV,yBAAyB,CAAC6B,KAAK,CAAC;;IAEpD;IACA,MAAMC,aAAa,GAAG,CAAAnB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEpC,YAAY,KAAI,EAAE;IACxD,MAAMI,cAAc,GAAG,CAAAgC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEhC,cAAc,KAAI,EAAE;;IAE3D;IACA,MAAMoD,eAAe,GAAGtG,gBAAgB,CAACuG,MAAM,CAAC,CAACC,GAAG,EAAE7D,MAAM,KAAK;MAC7D6D,GAAG,CAAC7D,MAAM,CAAC1C,GAAG,CAAC,GAAG0C,MAAM,CAACzC,KAAK;MAC9B,OAAOsG,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,MAAMC,YAAY,GAAGJ,aAAa,CAC7B3D,GAAG,CAAEG,IAAI,IAAKyD,eAAe,CAACzD,IAAI,CAAC,IAAIA,IAAI,CAAC,CAC5C6D,IAAI,CAAC,IAAI,CAAC;IAEf,MAAM3C,UAAU,GAAG;MACfoB,UAAU,EAAEtB,SAAS;MACrBuB,UAAU,EAAE,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,KAAI,EAAE;MACnCC,WAAW,EAAE;QACTD,IAAI,EAAE,CAAAH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,IAAI,KAAI,EAAE;QAChCtD,KAAK,EAAE,CAAAmD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEnD,KAAK,KAAI,IAAI;QACpC4E,IAAI,EAAE,CAAAzB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyB,IAAI,KAAI;MAClC,CAAC;MACDpB,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAEvC,cAAc;MACnCwC,aAAa,EAAEe;IACnB,CAAC;;IAED;IACA3C,sBAAsB,CAACC,UAAU,CAAC;IAElCJ,aAAa,CAAEoB,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACqB,KAAK,GAAG;IACb,CAAC,CAAC,CAAC;IAEHxC,WAAW,CAAEmB,IAAI,IACbA,IAAI,CAACrC,GAAG,CAAC,CAACrC,GAAG,EAAEoE,CAAC,KACZA,CAAC,KAAK2B,KAAK,GACL;MACI,GAAG/F,GAAG;MACNe,mBAAmB,EAAE,KAAK;MAC1B0B,YAAY,EAAE,IAAI;MAClBI,cAAc,EAAE,EAAE;MAClB0D,iBAAiB,EAAE,IAAI;MACvB9B,eAAe,EAAE,IAAI;MACrBE,UAAU,EAAE;IAChB,CAAC,GACD3E,GACV,CACJ,CAAC;EACL,CAAC;EAED,oBACItB,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,2BAA2B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACjCiB,YAAY,gBACT1E,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAM,CAAC,CAAC;EAAA,eAE/CzD,KAAA,CAAAiD,aAAA;IAAK6E,KAAK,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAA5E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClDzD,KAAA,CAAAiD,aAAA,CAAClC,wBAAwB;IACrBuF,IAAI,EAAE3F,OAAO,CAAC2F,IAAK;IACnBtD,KAAK,EAAErC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqC,KAAM;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACzB,CACA,CAER,CAAC,EAEL,CAAC9C,OAAO,CAACsH,SAAS,iBACfjI,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,oEAAoE;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/EzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,gCAAgC;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,wBAAwB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnCzD,KAAA,CAAAiD,aAAA;IACIE,SAAS,EAAE,+BACPwB,UAAU,CAACtD,GAAG,CAAC,IACfV,OAAO,CAACsF,UAAU,KAAK,UAAU,GAC3B,oCAAoC,GACpC,EAAE,IACRtF,OAAO,CAAC0B,mBAAmB,IAAI1B,OAAO,CAACkH,iBAAiB,GAAG,WAAW,GAAG,EAAE,IAC3ElH,OAAO,CAACoF,eAAe,GACjB,UAAU,GACV,EAAE,EACT;IACHmC,KAAK,EACDvH,OAAO,CAACoF,eAAe,GACjB,eAAe,GACf,SACT;IACDoC,QAAQ,EAAExH,OAAO,CAACoF,eAAgB;IAClCrC,OAAO,EAAEA,CAAA,KACLmC,0BAA0B,CAACxE,GAAG,CACjC;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEDzD,KAAA,CAAAiD,aAAA,CAACpC,OAAO;IAACuH,SAAS,EAAC,QAAQ;IAACF,KAAK,EAAC,SAAS;IAAA9E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvCzD,KAAA,CAAAiD,aAAA,CAAC7C,UAAU;IACPiI,MAAM,EACF1D,UAAU,CAACtD,GAAG,CAAC,IACfV,OAAO,CAACsF,UAAU,KACd,UAAU,GACR,OAAO,GACPqC,SACT;IACDC,WAAW,EACP5D,UAAU,CAACtD,GAAG,CAAC,IACfV,OAAO,CAACsF,UAAU,KACd,UAAU,GACR,CAAC,GACD,CACT;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACJ,CACI,CACL,CAAC,eAETzD,KAAA,CAAAiD,aAAA;IACIE,SAAS,EAAE;AAC/C,8EAA8ExC,OAAO,CAACkH,iBAAiB,IAAIlH,OAAO,CAAC0B,mBAAmB,IAAI1B,OAAO,CAACsF,UAAU,KAAK,UAAU,GAAG,wCAAwC,GAAG,EAAE;AAC3N,8EAA8EtB,UAAU,CAACtD,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,IAChCV,OAAO,CAACoF,eAAe,GACjB,UAAU,GACV,EAAE,EACT;IAC3CmC,KAAK,EACDvH,OAAO,CAACoF,eAAe,GACjB,eAAe,GACf,aACT;IACDoC,QAAQ,EAAExH,OAAO,CAACoF,eAAgB;IAClCrC,OAAO,EAAEA,CAAA,KAAMnC,qBAAqB,CAACF,GAAG,CAAE;IAAA+B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1CzD,KAAA,CAAAiD,aAAA,CAACpC,OAAO;IACJuH,SAAS,EAAC,QAAQ;IAClBF,KAAK,EAAC,aAAa;IAAA9E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEnBzD,KAAA,CAAAiD,aAAA,CAAC5C,YAAY;IACTmI,KAAK,EACD7H,OAAO,CAAC0B,mBAAmB,IAC3B1B,OAAO,CAACsF,UAAU,KACd,UAAU,GACR,OAAO,GACPqC,SACT;IAAAlF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACJ,CACI,CACL,CACP,CAAC,EACL9C,OAAO,CAACkH,iBAAiB,iBACtB7H,KAAA,CAAAiD,aAAA,CAACzC,KAAK;IACFiI,QAAQ;IACR9H,OAAO,EAAC,8BAA8B;IACtCmD,IAAI,EAAC,SAAS;IACdX,SAAS,EAAC,8CAA8C;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC3D,CAEJ,CAAC,eACNzD,KAAA,CAAAiD,aAAA;IAAKE,SAAS,EAAC,4BAA4B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC9C,OAAO,CAACiH,IACR,CACJ,CACR,EAGAjH,OAAO,CAAC0B,mBAAmB,iBACxBrC,KAAA,CAAAiD,aAAA,CAAC7B,eAAe;IACZC,GAAG,EAAEA,GAAI;IACTC,GAAG,EAAEX,OAAQ;IACbY,qBAAqB,EAAEA,qBAAsB;IAC7CC,mBAAmB,EAAEA,mBAAoB;IACzCC,0BAA0B,EAAEA,0BAA2B;IACvDC,0BAA0B,EAAEA,0BAA2B;IACvDC,QAAQ,EAAEA,QAAS;IAAAyB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACtB,CAgCJ,CACJ,CAAC;AAEd,CAAC;AAED,eAAegB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}