import React from 'react';
import {
    convertUTCToDisplayTime,
    isTodayDate,
    isYesterdayDate,
} from '../../util/helpers';
import { TaskRminderTemplate } from './NotificationTemplates/TaskReminder';
import { TaskAssigneeAndReassignTemplate } from './NotificationTemplates/TaskAssigneeAndReassignTemplate';
import { DueTaskTemplate } from './NotificationTemplates/DueTaskTemplate';
import { TaskDeletionTemplate } from './NotificationTemplates/TaskDeletionTemplate';
import { SrvcReqStatusUpdateViaVA } from './NotificationTemplates/SrvcReqStatusUpdateViaVA';

const NotificationItem = ({
    notification,
    markAsRead,
    removeNotifcationFromApp,
}) => {
    const { notification_type, notification_time } = notification;
    const isCurrentDate = isTodayDate(notification_time);
    const isYesterday = isYesterdayDate(notification_time);

    let showTimeonly = false,
        showDateOnly = false;
    if (isCurrentDate) {
        showTimeonly = true;
    } else {
        showDateOnly = true;
    }

    let notiTimeLabel = isYesterday
        ? 'Yesterday'
        : `${convertUTCToDisplayTime(
              notification_time,
              showDateOnly,
              showTimeonly
          )}`;
    let element;

    switch (notification_type) {
        case 'my_tasks_noti':
            element = DueTaskTemplate(
                notification,
                markAsRead,
                removeNotifcationFromApp,
                notiTimeLabel,
                isCurrentDate
            );
            break;
        case 'task_assignee':
            element = TaskAssigneeAndReassignTemplate(
                notification,
                markAsRead,
                removeNotifcationFromApp,
                notiTimeLabel
            );
            break;
        case 'task_deletion':
            element = TaskDeletionTemplate(
                notification,
                markAsRead,
                removeNotifcationFromApp,
                notiTimeLabel
            );
            break;
        case 'task_reassignee':
            element = TaskAssigneeAndReassignTemplate(
                notification,
                markAsRead,
                removeNotifcationFromApp,
                notiTimeLabel
            );
            break;
        case 'task_reminder':
            element = TaskRminderTemplate(
                notification,
                markAsRead,
                removeNotifcationFromApp,
                notiTimeLabel
            );
        case 'srvc_req_status_update_thr_va':
            element = SrvcReqStatusUpdateViaVA(
                notification,
                markAsRead,
                removeNotifcationFromApp,
                notiTimeLabel
            );
    }

    return (
        <>
            <div className="wifyNotification gx-my-1">{element}</div>
        </>
    );
};

export default NotificationItem;
